"""
Institutional-Retail Lag Analysis Framework
The Architect's Fix #1: Define Your Actual Edge

This module measures the time delay between institutional options activity
and subsequent retail equity moves to quantify the exploitable lag.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from scipy import stats

logger = logging.getLogger(__name__)

@dataclass
class LagAnalysisResult:
    """Results from lag analysis between institutional and retail flow"""
    optimal_lag_minutes: int
    correlation_coefficient: float
    statistical_significance: float  # p-value
    sample_size: int
    confidence_interval: Tuple[float, float]
    profit_potential_bps: float  # basis points
    win_rate_at_optimal_lag: float

@dataclass
class InstitutionalEvent:
    """Represents a detected institutional flow event"""
    timestamp: datetime
    symbol: str
    flow_type: str  # 'call_buying', 'put_buying', 'call_selling', 'put_selling'
    notional_crores: float
    volume_sigma: float  # how many standard deviations above normal
    confidence: float

@dataclass
class RetailResponse:
    """Represents subsequent retail market response"""
    timestamp: datetime
    symbol: str
    price_change_bps: float  # basis points change in underlying
    volume_change_ratio: float  # ratio vs normal volume
    direction: str  # 'bullish', 'bearish', 'neutral'

class LagAnalysisFramework:
    """
    Analyzes lag between institutional options flow and retail equity response
    to define quantifiable trading edge
    """

    def __init__(self, config: Dict = None):
        self.config = config or {}

        # Analysis parameters
        self.max_lag_hours = self.config.get('max_lag_hours', 24)
        self.min_institutional_threshold_crores = self.config.get('min_threshold_crores', 50)
        self.min_volume_sigma = self.config.get('min_volume_sigma', 3.0)
        self.min_price_move_bps = self.config.get('min_price_move_bps', 20)

        # Statistical significance requirements
        self.min_correlation = self.config.get('min_correlation', 0.3)
        self.max_p_value = self.config.get('max_p_value', 0.05)
        self.min_sample_size = self.config.get('min_sample_size', 50)

    def analyze_institutional_retail_lag(
        self,
        institutional_events: List[InstitutionalEvent],
        retail_responses: List[RetailResponse]
    ) -> Dict[str, LagAnalysisResult]:
        """
        Analyze lag between institutional events and retail responses

        Args:
            institutional_events: List of institutional flow events
            retail_responses: List of retail market responses

        Returns:
            Dictionary mapping symbols to lag analysis results
        """
        results = {}

        # Group by symbol
        symbols = set([event.symbol for event in institutional_events])

        for symbol in symbols:
            logger.info(f"Analyzing lag for {symbol}")

            # Filter events for this symbol
            symbol_institutional = [e for e in institutional_events if e.symbol == symbol]
            symbol_retail = [r for r in retail_responses if r.symbol == symbol]

            if len(symbol_institutional) < self.min_sample_size:
                logger.warning(f"Insufficient institutional events for {symbol}: {len(symbol_institutional)}")
                continue

            # Analyze lag for this symbol
            result = self._analyze_symbol_lag(symbol_institutional, symbol_retail)

            if result and result.statistical_significance <= self.max_p_value:
                results[symbol] = result
                logger.info(f"Found significant lag for {symbol}: {result.optimal_lag_minutes} minutes")
            else:
                logger.warning(f"No significant lag found for {symbol}")

        return results

    def _analyze_symbol_lag(
        self,
        institutional_events: List[InstitutionalEvent],
        retail_responses: List[RetailResponse]
    ) -> Optional[LagAnalysisResult]:
        """
        Analyze lag for a specific symbol

        Args:
            institutional_events: Institutional events for symbol
            retail_responses: Retail responses for symbol

        Returns:
            LagAnalysisResult if significant lag found, None otherwise
        """
        # Test different lag periods
        lag_minutes_to_test = range(5, self.max_lag_hours * 60, 5)  # 5-minute intervals

        best_correlation = 0
        best_lag = 0
        best_p_value = 1.0
        best_profit_potential = 0
        best_win_rate = 0

        for lag_minutes in lag_minutes_to_test:
            correlation, p_value, profit_bps, win_rate = self._test_lag_period(
                institutional_events, retail_responses, lag_minutes
            )

            if correlation > best_correlation and p_value <= self.max_p_value:
                best_correlation = correlation
                best_lag = lag_minutes
                best_p_value = p_value
                best_profit_potential = profit_bps
                best_win_rate = win_rate

        if best_correlation >= self.min_correlation and best_p_value <= self.max_p_value:
            # Calculate confidence interval
            n = len(institutional_events)
            confidence_interval = self._calculate_confidence_interval(best_correlation, n)

            return LagAnalysisResult(
                optimal_lag_minutes=best_lag,
                correlation_coefficient=best_correlation,
                statistical_significance=best_p_value,
                sample_size=n,
                confidence_interval=confidence_interval,
                profit_potential_bps=best_profit_potential,
                win_rate_at_optimal_lag=best_win_rate
            )

        return None

    def _test_lag_period(
        self,
        institutional_events: List[InstitutionalEvent],
        retail_responses: List[RetailResponse],
        lag_minutes: int
    ) -> Tuple[float, float, float, float]:
        """
        Test correlation at specific lag period

        Args:
            institutional_events: Institutional events
            retail_responses: Retail responses
            lag_minutes: Lag period to test in minutes

        Returns:
            Tuple of (correlation, p_value, profit_potential_bps, win_rate)
        """
        matched_pairs = []

        for inst_event in institutional_events:
            # Look for retail response within lag window
            target_time = inst_event.timestamp + timedelta(minutes=lag_minutes)
            window_start = target_time - timedelta(minutes=2.5)  # ±2.5 minute window
            window_end = target_time + timedelta(minutes=2.5)

            # Find retail responses in window
            matching_responses = [
                r for r in retail_responses
                if window_start <= r.timestamp <= window_end
            ]

            if matching_responses:
                # Take the response with largest price move
                best_response = max(matching_responses, key=lambda x: abs(x.price_change_bps))

                # Determine if institutional flow predicts retail direction
                inst_direction = self._get_institutional_direction(inst_event)
                retail_direction = 1 if best_response.price_change_bps > 0 else -1

                matched_pairs.append({
                    'institutional_signal': inst_direction,
                    'retail_response': retail_direction,
                    'price_change_bps': best_response.price_change_bps,
                    'institutional_strength': inst_event.volume_sigma
                })

        if len(matched_pairs) < 10:  # Need minimum sample
            return 0, 1.0, 0, 0

        # Calculate correlation
        inst_signals = [p['institutional_signal'] for p in matched_pairs]
        retail_responses = [p['retail_response'] for p in matched_pairs]

        correlation, p_value = stats.pearsonr(inst_signals, retail_responses)

        # Calculate profit potential and win rate
        profit_bps = np.mean([
            p['price_change_bps'] * p['institutional_signal']
            for p in matched_pairs
        ])

        win_rate = np.mean([
            1 if p['institutional_signal'] * p['retail_response'] > 0 else 0
            for p in matched_pairs
        ])

        return abs(correlation), p_value, profit_bps, win_rate

    def _get_institutional_direction(self, event: InstitutionalEvent) -> int:
        """
        Convert institutional flow type to directional signal

        Args:
            event: Institutional event

        Returns:
            1 for bullish, -1 for bearish
        """
        if event.flow_type in ['call_buying', 'put_selling']:
            return 1  # Bullish
        elif event.flow_type in ['put_buying', 'call_selling']:
            return -1  # Bearish
        else:
            return 0  # Neutral

    def _calculate_confidence_interval(self, correlation: float, n: int) -> Tuple[float, float]:
        """
        Calculate 95% confidence interval for correlation coefficient

        Args:
            correlation: Correlation coefficient
            n: Sample size

        Returns:
            Tuple of (lower_bound, upper_bound)
        """
        # Fisher z-transformation
        z = 0.5 * np.log((1 + correlation) / (1 - correlation))
        se = 1 / np.sqrt(n - 3)

        # 95% confidence interval
        z_lower = z - 1.96 * se
        z_upper = z + 1.96 * se

        # Transform back
        lower = (np.exp(2 * z_lower) - 1) / (np.exp(2 * z_lower) + 1)
        upper = (np.exp(2 * z_upper) - 1) / (np.exp(2 * z_upper) + 1)

        return (lower, upper)

    def generate_trading_rules(self, lag_results: Dict[str, LagAnalysisResult]) -> Dict[str, Dict]:
        """
        Generate mechanical trading rules based on lag analysis

        Args:
            lag_results: Results from lag analysis

        Returns:
            Dictionary of trading rules per symbol
        """
        trading_rules = {}

        for symbol, result in lag_results.items():
            if result.correlation_coefficient >= self.min_correlation:
                trading_rules[symbol] = {
                    'entry_lag_minutes': result.optimal_lag_minutes,
                    'min_institutional_threshold_crores': self.min_institutional_threshold_crores,
                    'min_volume_sigma': self.min_volume_sigma,
                    'expected_profit_bps': result.profit_potential_bps,
                    'expected_win_rate': result.win_rate_at_optimal_lag,
                    'statistical_confidence': 1 - result.statistical_significance,
                    'position_sizing_factor': min(result.correlation_coefficient, 0.5),  # Cap at 50%
                    'stop_loss_bps': result.profit_potential_bps * 0.5,  # 50% of expected profit
                    'take_profit_bps': result.profit_potential_bps * 1.5,  # 150% of expected profit
                }

        return trading_rules