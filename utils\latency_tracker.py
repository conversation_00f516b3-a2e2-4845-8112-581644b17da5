"""
Microsecond-precision latency tracking for real-time trading systems
Critical for detecting when the system becomes too slow for manipulation detection
"""
import time
import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics
import threading
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

@dataclass
class LatencyMeasurement:
    """Single latency measurement with microsecond precision"""
    operation: str
    start_time_ns: int
    end_time_ns: int
    duration_us: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration_ms(self) -> float:
        """Duration in milliseconds"""
        return self.duration_us / 1000.0
    
    @property
    def is_critical(self) -> bool:
        """Check if latency exceeds critical threshold (100ms)"""
        return self.duration_ms > 100.0

@dataclass
class LatencyStats:
    """Statistical summary of latency measurements"""
    operation: str
    count: int
    mean_us: float
    median_us: float
    p95_us: float
    p99_us: float
    max_us: float
    min_us: float
    critical_count: int
    
    @property
    def mean_ms(self) -> float:
        return self.mean_us / 1000.0
    
    @property
    def p95_ms(self) -> float:
        return self.p95_us / 1000.0
    
    @property
    def critical_rate(self) -> float:
        """Percentage of measurements exceeding 100ms"""
        return (self.critical_count / max(self.count, 1)) * 100.0

class LatencyTracker:
    """
    High-precision latency tracker for real-time trading systems
    Tracks every operation from data collection to trade execution
    """
    
    def __init__(self, max_measurements_per_operation: int = 10000):
        self.max_measurements = max_measurements_per_operation
        self.measurements: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_measurements_per_operation))
        self.active_operations: Dict[str, int] = {}
        self.lock = threading.RLock()
        
        # Critical thresholds (microseconds) - The Architect's realistic targets
        self.critical_thresholds = {
            'api_request': 500_000,          # 500ms for NSE API calls (realistic)
            'data_collection': 200_000,      # 200ms for complete data collection
            'data_processing': 50_000,       # 50ms for data processing
            'detection_processing': 30_000,  # 30ms for detection algorithms
            'signal_generation': 10_000,     # 10ms for signal generation
            'trade_decision': 5_000,         # 5ms for trade decision logic
            'order_preparation': 10_000,     # 10ms for order preparation
            'order_submission': 100_000,     # 100ms for order submission to broker
            'market_data_to_signal': 300_000, # 300ms from market data to signal
            'signal_to_order': 50_000,       # 50ms from signal to order submission
            'end_to_end_pipeline': 800_000,  # 800ms total realistic pipeline
        }
        
        # Performance alerts
        self.alert_callbacks: List[callable] = []
        
    def start_operation(self, operation: str, metadata: Dict[str, Any] = None) -> str:
        """
        Start timing an operation
        
        Args:
            operation: Name of the operation being timed
            metadata: Additional context for the operation
            
        Returns:
            Unique operation ID for this timing session
        """
        operation_id = f"{operation}_{time.time_ns()}"
        start_time = time.time_ns()
        
        with self.lock:
            self.active_operations[operation_id] = start_time
            
        return operation_id
    
    def end_operation(self, operation_id: str, metadata: Dict[str, Any] = None) -> Optional[LatencyMeasurement]:
        """
        End timing an operation and record the measurement
        
        Args:
            operation_id: ID returned from start_operation
            metadata: Additional metadata to store with measurement
            
        Returns:
            LatencyMeasurement if operation was found, None otherwise
        """
        end_time = time.time_ns()
        
        with self.lock:
            if operation_id not in self.active_operations:
                logger.warning(f"Unknown operation ID: {operation_id}")
                return None
                
            start_time = self.active_operations.pop(operation_id)
            
        # Extract operation name from ID
        operation = operation_id.rsplit('_', 1)[0]
        duration_ns = end_time - start_time
        duration_us = duration_ns / 1000.0
        
        measurement = LatencyMeasurement(
            operation=operation,
            start_time_ns=start_time,
            end_time_ns=end_time,
            duration_us=duration_us,
            metadata=metadata or {}
        )
        
        # Store measurement
        with self.lock:
            self.measurements[operation].append(measurement)
        
        # Check for critical latency
        if measurement.is_critical:
            self._trigger_latency_alert(measurement)
            
        return measurement
    
    @asynccontextmanager
    async def measure_async(self, operation: str, metadata: Dict[str, Any] = None):
        """
        Async context manager for measuring operation latency
        
        Usage:
            async with latency_tracker.measure_async('data_collection'):
                data = await collect_data()
        """
        operation_id = self.start_operation(operation, metadata)
        try:
            yield
        finally:
            self.end_operation(operation_id, metadata)
    
    def measure_sync(self, operation: str, metadata: Dict[str, Any] = None):
        """
        Synchronous context manager for measuring operation latency
        
        Usage:
            with latency_tracker.measure_sync('calculation'):
                result = expensive_calculation()
        """
        return _SyncLatencyContext(self, operation, metadata)
    
    def get_stats(self, operation: str) -> Optional[LatencyStats]:
        """
        Get statistical summary for an operation
        
        Args:
            operation: Name of the operation
            
        Returns:
            LatencyStats if measurements exist, None otherwise
        """
        with self.lock:
            if operation not in self.measurements or not self.measurements[operation]:
                return None
                
            measurements = list(self.measurements[operation])
        
        durations = [m.duration_us for m in measurements]
        critical_count = sum(1 for m in measurements if m.is_critical)
        
        return LatencyStats(
            operation=operation,
            count=len(durations),
            mean_us=statistics.mean(durations),
            median_us=statistics.median(durations),
            p95_us=self._percentile(durations, 95),
            p99_us=self._percentile(durations, 99),
            max_us=max(durations),
            min_us=min(durations),
            critical_count=critical_count
        )
    
    def get_all_stats(self) -> Dict[str, LatencyStats]:
        """Get stats for all tracked operations"""
        with self.lock:
            operations = list(self.measurements.keys())
        
        return {op: self.get_stats(op) for op in operations if self.get_stats(op)}
    
    def get_recent_critical_events(self, minutes: int = 5) -> List[LatencyMeasurement]:
        """Get recent measurements that exceeded critical thresholds"""
        cutoff_time = time.time_ns() - (minutes * 60 * 1_000_000_000)
        critical_events = []
        
        with self.lock:
            for operation, measurements in self.measurements.items():
                for measurement in measurements:
                    if (measurement.start_time_ns >= cutoff_time and 
                        measurement.is_critical):
                        critical_events.append(measurement)
        
        return sorted(critical_events, key=lambda x: x.start_time_ns, reverse=True)
    
    def is_system_healthy(self) -> bool:
        """
        Check if system latency is within acceptable bounds
        
        Returns:
            True if system is performing within thresholds
        """
        # Check recent critical events
        recent_critical = self.get_recent_critical_events(minutes=1)
        if len(recent_critical) > 5:  # More than 5 critical events in 1 minute
            return False
        
        # Check key operation performance
        key_operations = ['data_collection', 'detection_processing', 'end_to_end']
        for operation in key_operations:
            stats = self.get_stats(operation)
            if stats and stats.critical_rate > 10.0:  # More than 10% critical
                return False
        
        return True
    
    def add_alert_callback(self, callback: callable):
        """Add callback function for latency alerts"""
        self.alert_callbacks.append(callback)
    
    def _trigger_latency_alert(self, measurement: LatencyMeasurement):
        """Trigger alerts for critical latency measurements"""
        logger.warning(
            f"CRITICAL LATENCY: {measurement.operation} took {measurement.duration_ms:.2f}ms "
            f"(threshold: {self.critical_thresholds.get(measurement.operation, 100000) / 1000:.1f}ms)"
        )
        
        for callback in self.alert_callbacks:
            try:
                callback(measurement)
            except Exception as e:
                logger.error(f"Error in latency alert callback: {e}")
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = (percentile / 100.0) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def reset_measurements(self, operation: str = None):
        """Reset measurements for an operation or all operations"""
        with self.lock:
            if operation:
                if operation in self.measurements:
                    self.measurements[operation].clear()
            else:
                self.measurements.clear()
                self.active_operations.clear()

    def start_end_to_end_pipeline(self, pipeline_id: str = None) -> str:
        """
        Start measuring complete end-to-end pipeline latency
        From market data event to order submission

        Args:
            pipeline_id: Optional custom pipeline ID

        Returns:
            Pipeline ID for tracking
        """
        if not pipeline_id:
            pipeline_id = f"pipeline_{time.time_ns()}"

        return self.start_operation('end_to_end_pipeline', {'pipeline_id': pipeline_id})

    def measure_api_latency(self, api_name: str, url: str = None):
        """
        Context manager for measuring API call latency

        Usage:
            async with latency_tracker.measure_api_latency('nse_options'):
                response = await api_call()
        """
        metadata = {'api_name': api_name}
        if url:
            metadata['url'] = url
        return self.measure_async('api_request', metadata)

    def get_pipeline_breakdown(self, pipeline_id: str) -> Dict[str, float]:
        """
        Get latency breakdown for a complete pipeline

        Args:
            pipeline_id: Pipeline ID to analyze

        Returns:
            Dictionary with latency breakdown in milliseconds
        """
        breakdown = {}

        with self.lock:
            for operation, measurements in self.measurements.items():
                for measurement in measurements:
                    if (measurement.metadata.get('pipeline_id') == pipeline_id or
                        measurement.metadata.get('operation_id') == pipeline_id):
                        breakdown[operation] = measurement.duration_ms

        return breakdown

    def get_critical_path_analysis(self) -> Dict[str, Any]:
        """
        Analyze the critical path in the trading pipeline
        Identifies bottlenecks and optimization opportunities

        Returns:
            Analysis of pipeline performance
        """
        analysis = {
            'bottlenecks': [],
            'total_pipeline_time': 0.0,
            'api_latency_impact': 0.0,
            'processing_efficiency': 0.0,
            'recommendations': []
        }

        # Get recent stats for key operations
        key_operations = [
            'api_request', 'data_collection', 'detection_processing',
            'signal_generation', 'trade_decision', 'order_submission'
        ]

        operation_stats = {}
        total_time = 0.0

        for operation in key_operations:
            stats = self.get_stats(operation)
            if stats:
                operation_stats[operation] = stats
                total_time += stats.mean_ms

        analysis['total_pipeline_time'] = total_time

        # Identify bottlenecks (operations taking >30% of total time)
        for operation, stats in operation_stats.items():
            if stats.mean_ms > (total_time * 0.3):
                analysis['bottlenecks'].append({
                    'operation': operation,
                    'latency_ms': stats.mean_ms,
                    'percentage_of_total': (stats.mean_ms / total_time) * 100,
                    'critical_rate': stats.critical_rate
                })

        # Calculate API impact
        api_stats = operation_stats.get('api_request')
        if api_stats:
            analysis['api_latency_impact'] = (api_stats.mean_ms / total_time) * 100

        # Processing efficiency (non-API time / total time)
        processing_time = total_time - (api_stats.mean_ms if api_stats else 0)
        analysis['processing_efficiency'] = (processing_time / total_time) * 100 if total_time > 0 else 0

        # Generate recommendations
        if analysis['api_latency_impact'] > 60:
            analysis['recommendations'].append("API latency is the primary bottleneck. Consider caching or faster data sources.")

        if any(b['critical_rate'] > 20 for b in analysis['bottlenecks']):
            analysis['recommendations'].append("High critical rate detected. System may be overloaded.")

        if analysis['processing_efficiency'] < 30:
            analysis['recommendations'].append("Processing efficiency is low. Optimize algorithms or increase hardware resources.")

        return analysis

class _SyncLatencyContext:
    """Synchronous context manager for latency measurement"""
    
    def __init__(self, tracker: LatencyTracker, operation: str, metadata: Dict[str, Any] = None):
        self.tracker = tracker
        self.operation = operation
        self.metadata = metadata
        self.operation_id = None
    
    def __enter__(self):
        self.operation_id = self.tracker.start_operation(self.operation, self.metadata)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.operation_id:
            self.tracker.end_operation(self.operation_id, self.metadata)

# Global latency tracker instance
latency_tracker = LatencyTracker()
