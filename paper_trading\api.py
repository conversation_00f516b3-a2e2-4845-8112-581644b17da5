#!/usr/bin/env python3
"""
Paper Trading API endpoints
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

from paper_trading.paper_trader import paper_trading_engine, PaperTrade, TradeStatus
from models.data_models import APIResponse

logger = logging.getLogger(__name__)

# Create router for paper trading endpoints
paper_trading_router = APIRouter(prefix="/paper-trading", tags=["Paper Trading"])

@paper_trading_router.get("/status", response_model=APIResponse)
async def get_paper_trading_status():
    """Get current paper trading status and performance"""
    try:
        performance = paper_trading_engine.get_performance_summary()
        
        return APIResponse(
            success=True,
            message="Paper trading status retrieved",
            data={
                "status": "active" if paper_trading_engine.available_capital > 0 else "inactive",
                "performance": performance,
                "open_trades_count": len(paper_trading_engine.open_trades),
                "closed_trades_count": len(paper_trading_engine.closed_trades)
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting paper trading status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@paper_trading_router.get("/performance", response_model=APIResponse)
async def get_performance_summary():
    """Get detailed performance summary"""
    try:
        performance = paper_trading_engine.get_performance_summary()
        
        # Calculate additional metrics
        recent_trades = [t for t in paper_trading_engine.closed_trades 
                        if t.exit_time and t.exit_time > datetime.now() - timedelta(days=7)]
        
        weekly_pnl = sum(t.actual_profit for t in recent_trades)
        
        return APIResponse(
            success=True,
            message="Performance summary retrieved",
            data={
                "overall_performance": performance,
                "weekly_performance": {
                    "trades_count": len(recent_trades),
                    "total_pnl": weekly_pnl,
                    "average_pnl": weekly_pnl / max(len(recent_trades), 1)
                },
                "risk_metrics": {
                    "current_exposure": sum(t.quantity * t.entry_price * 50 for t in paper_trading_engine.open_trades.values()),
                    "max_position_size_percent": paper_trading_engine.max_position_size * 100,
                    "stop_loss_percent": paper_trading_engine.stop_loss_percent * 100,
                    "take_profit_percent": paper_trading_engine.take_profit_percent * 100
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting performance summary: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@paper_trading_router.get("/trades", response_model=APIResponse)
async def get_trades(
    status: Optional[str] = Query(None, description="Filter by trade status (OPEN/CLOSED)"),
    limit: int = Query(50, description="Maximum number of trades to return"),
    hours: int = Query(24, description="Hours to look back for trades")
):
    """Get paper trades with optional filtering"""
    try:
        all_trades = []
        
        # Add open trades
        if not status or status.upper() == "OPEN":
            all_trades.extend(list(paper_trading_engine.open_trades.values()))
        
        # Add closed trades
        if not status or status.upper() == "CLOSED":
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_closed = [t for t in paper_trading_engine.closed_trades 
                           if t.exit_time and t.exit_time > cutoff_time]
            all_trades.extend(recent_closed)
        
        # Sort by entry time (newest first) and limit
        all_trades.sort(key=lambda x: x.entry_time, reverse=True)
        all_trades = all_trades[:limit]
        
        # Convert to serializable format
        trades_data = []
        for trade in all_trades:
            trade_data = {
                "id": trade.id,
                "signal_id": trade.signal_id,
                "symbol": trade.symbol,
                "strike": trade.strike,
                "option_type": trade.option_type,
                "action": trade.action.value,
                "entry_price": trade.entry_price,
                "exit_price": trade.exit_price,
                "quantity": trade.quantity,
                "entry_time": trade.entry_time.isoformat(),
                "exit_time": trade.exit_time.isoformat() if trade.exit_time else None,
                "status": trade.status.value,
                "profit_loss": trade.profit_loss,
                "profit_loss_percent": trade.profit_loss_percent * 100,  # Convert to percentage
                "actual_profit": trade.actual_profit,
                "confidence": trade.confidence,
                "manipulation_type": trade.manipulation_type,
                "estimated_profit": trade.estimated_profit,
                "trade_reason": trade.trade_reason,
                "duration_minutes": (
                    (trade.exit_time - trade.entry_time).total_seconds() / 60 
                    if trade.exit_time else 
                    (datetime.now() - trade.entry_time).total_seconds() / 60
                )
            }
            trades_data.append(trade_data)
        
        return APIResponse(
            success=True,
            message=f"Retrieved {len(trades_data)} trades",
            data={
                "trades": trades_data,
                "total_count": len(trades_data),
                "filters": {
                    "status": status,
                    "hours": hours,
                    "limit": limit
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting trades: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@paper_trading_router.get("/trades/{trade_id}", response_model=APIResponse)
async def get_trade_details(trade_id: str):
    """Get detailed information about a specific trade"""
    try:
        # Look for trade in open trades
        trade = paper_trading_engine.open_trades.get(trade_id)
        
        # If not found, look in closed trades
        if not trade:
            trade = next((t for t in paper_trading_engine.closed_trades if t.id == trade_id), None)
        
        if not trade:
            raise HTTPException(status_code=404, detail="Trade not found")
        
        # Calculate current metrics for open trades
        current_pnl = trade.profit_loss
        current_pnl_percent = trade.profit_loss_percent * 100
        
        trade_details = {
            "id": trade.id,
            "signal_id": trade.signal_id,
            "symbol": trade.symbol,
            "strike": trade.strike,
            "option_type": trade.option_type,
            "action": trade.action.value,
            "entry_price": trade.entry_price,
            "exit_price": trade.exit_price,
            "quantity": trade.quantity,
            "entry_time": trade.entry_time.isoformat(),
            "exit_time": trade.exit_time.isoformat() if trade.exit_time else None,
            "status": trade.status.value,
            "profit_loss": current_pnl,
            "profit_loss_percent": current_pnl_percent,
            "actual_profit": trade.actual_profit,
            "confidence": trade.confidence,
            "manipulation_type": trade.manipulation_type,
            "estimated_profit": trade.estimated_profit,
            "trade_reason": trade.trade_reason,
            "market_data": trade.market_data,
            "trade_value": trade.quantity * trade.entry_price * 50,
            "duration_minutes": (
                (trade.exit_time - trade.entry_time).total_seconds() / 60 
                if trade.exit_time else 
                (datetime.now() - trade.entry_time).total_seconds() / 60
            )
        }
        
        return APIResponse(
            success=True,
            message="Trade details retrieved",
            data=trade_details
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting trade details: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@paper_trading_router.get("/analytics", response_model=APIResponse)
async def get_trading_analytics():
    """Get comprehensive trading analytics and insights"""
    try:
        performance = paper_trading_engine.get_performance_summary()
        
        # Analyze trades by manipulation type
        manipulation_stats = {}
        for trade in paper_trading_engine.closed_trades:
            m_type = trade.manipulation_type
            if m_type not in manipulation_stats:
                manipulation_stats[m_type] = {
                    "count": 0,
                    "total_pnl": 0,
                    "winning_trades": 0,
                    "average_confidence": 0
                }
            
            manipulation_stats[m_type]["count"] += 1
            manipulation_stats[m_type]["total_pnl"] += trade.actual_profit
            if trade.actual_profit > 0:
                manipulation_stats[m_type]["winning_trades"] += 1
            manipulation_stats[m_type]["average_confidence"] += trade.confidence
        
        # Calculate averages
        for m_type in manipulation_stats:
            stats = manipulation_stats[m_type]
            stats["average_pnl"] = stats["total_pnl"] / max(stats["count"], 1)
            stats["win_rate"] = (stats["winning_trades"] / max(stats["count"], 1)) * 100
            stats["average_confidence"] = (stats["average_confidence"] / max(stats["count"], 1)) * 100
        
        # Time-based analysis
        daily_pnl = {}
        for trade in paper_trading_engine.closed_trades:
            if trade.exit_time:
                date_key = trade.exit_time.date().isoformat()
                if date_key not in daily_pnl:
                    daily_pnl[date_key] = 0
                daily_pnl[date_key] += trade.actual_profit
        
        return APIResponse(
            success=True,
            message="Trading analytics retrieved",
            data={
                "overall_performance": performance,
                "manipulation_type_analysis": manipulation_stats,
                "daily_pnl": daily_pnl,
                "risk_analysis": {
                    "largest_loss": min([t.actual_profit for t in paper_trading_engine.closed_trades], default=0),
                    "largest_gain": max([t.actual_profit for t in paper_trading_engine.closed_trades], default=0),
                    "average_trade_duration": sum([
                        (t.exit_time - t.entry_time).total_seconds() / 60 
                        for t in paper_trading_engine.closed_trades 
                        if t.exit_time
                    ]) / max(len(paper_trading_engine.closed_trades), 1),
                    "current_open_positions": len(paper_trading_engine.open_trades)
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting trading analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@paper_trading_router.post("/reset", response_model=APIResponse)
async def reset_paper_trading():
    """Reset paper trading engine to initial state"""
    try:
        # Store current session results before reset
        final_performance = paper_trading_engine.get_performance_summary()
        
        # Reset the engine
        initial_capital = paper_trading_engine.initial_capital
        paper_trading_engine.__init__(initial_capital)
        
        return APIResponse(
            success=True,
            message="Paper trading engine reset successfully",
            data={
                "previous_session_performance": final_performance,
                "new_session": {
                    "initial_capital": initial_capital,
                    "current_capital": initial_capital,
                    "available_capital": initial_capital
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error resetting paper trading: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
