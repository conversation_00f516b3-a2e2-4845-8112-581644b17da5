#!/usr/bin/env python3
"""
Yahoo Finance data collector for real market data
Alternative source when NSE API is not accessible
"""
import asyncio
import aiohttp
import logging
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import time

from models.data_models import OptionsData, OptionType
from utils.cache import cache_manager
from utils.exceptions import DataCollectionError, RateLimitError
from config.settings import settings

logger = logging.getLogger(__name__)

class YahooFinanceCollector:
    """
    Yahoo Finance data collector for real market data
    """
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.cache = cache_manager
        self.base_url = "https://query1.finance.yahoo.com/v7/finance/options"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        }
        
        # Symbol mapping for Indian indices
        self.symbol_mapping = {
            'NIFTY': '^NSEI',
            'BANKNIFTY': '^NSEBANK',
            'FINNIFTY': 'NIFTYFIN.NS'
        }
        
        self.request_count = 0
        self.error_count = 0
        self.last_request_time = None
    
    async def start_session(self):
        """Initialize aiohttp session"""
        connector = aiohttp.TCPConnector(
            limit=20,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self.headers
        )
        
        logger.info("Yahoo Finance collector session started")
    
    async def close_session(self):
        """Close aiohttp session"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("Yahoo Finance collector session closed")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close_session()
    
    async def _make_request(self, url: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make rate-limited request to Yahoo Finance API"""
        if not self.session:
            raise DataCollectionError("Session not initialized")

        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = f"yahoo_request:{url}:{str(params)}"
            cached_data = await self.cache.get(cache_key)
            if cached_data:
                logger.debug(f"Cache hit for {url}")
                return cached_data

            self.request_count += 1
            self.last_request_time = datetime.now()

            logger.info(f"Making Yahoo Finance API request to {url}")
            if params:
                logger.info(f"   Parameters: {params}")

            async with self.session.get(url, params=params) as response:
                response_time = time.time() - start_time

                if response.status == 200:
                    data = await response.json()
                    
                    logger.info(f"Yahoo Finance API success: {response.status} in {response_time:.3f}s")
                    
                    # Cache successful responses for 60 seconds
                    await self.cache.set(cache_key, data, ttl=60)
                    
                    return data
                    
                elif response.status == 429:
                    self.error_count += 1
                    logger.warning(f"Yahoo Finance API rate limited: {response.status} in {response_time:.3f}s")
                    raise RateLimitError(f"Rate limit exceeded: {response.status}")
                    
                else:
                    self.error_count += 1
                    error_text = await response.text()
                    logger.error(f"Yahoo Finance API error: {response.status} in {response_time:.3f}s - {error_text[:100]}")
                    raise DataCollectionError(f"HTTP {response.status}: {error_text}")
                    
        except asyncio.TimeoutError:
            self.error_count += 1
            raise DataCollectionError(f"Timeout requesting {url}")
        except aiohttp.ClientError as e:
            self.error_count += 1
            raise DataCollectionError(f"Client error: {str(e)}")
    
    async def get_options_chain(self, symbol: str) -> List[OptionsData]:
        """
        Get options chain data for a symbol from Yahoo Finance
        """
        try:
            # Map Indian symbols to Yahoo Finance symbols
            yahoo_symbol = self.symbol_mapping.get(symbol, symbol)
            
            # Get options data
            url = self.base_url
            params = {"symbol": yahoo_symbol}
            
            data = await self._make_request(url, params)
            
            if not data or "optionChain" not in data:
                raise DataCollectionError(f"Invalid options data for {symbol}")
            
            options_list = []
            option_chain = data["optionChain"]["result"][0]
            
            # Get underlying price
            underlying_price = option_chain.get("quote", {}).get("regularMarketPrice", 0)
            
            # Process options data
            for expiry_data in option_chain.get("options", []):
                expiry_timestamp = expiry_data.get("expirationDate", 0)
                expiry_date = datetime.fromtimestamp(expiry_timestamp)
                
                # Process calls
                for call_data in expiry_data.get("calls", []):
                    options_list.append(self._create_option_data(
                        symbol, call_data, OptionType.CE, expiry_date
                    ))
                
                # Process puts
                for put_data in expiry_data.get("puts", []):
                    options_list.append(self._create_option_data(
                        symbol, put_data, OptionType.PE, expiry_date
                    ))
            
            logger.info(f"Collected {len(options_list)} options data points for {symbol} from Yahoo Finance")
            
            if options_list:
                # Show sample of current prices
                logger.info(f"SAMPLE CURRENT PRICES for {symbol} (Yahoo Finance):")
                for option in options_list[:3]:  # Show first 3 options
                    logger.info(
                        f"   {option.strike} {option.option_type.value}: "
                        f"${option.last_price:.2f} | Vol: {option.volume:,} | OI: {option.open_interest:,}"
                    )

            return options_list
            
        except Exception as e:
            logger.error(f"Error collecting options data for {symbol} from Yahoo Finance: {str(e)}")
            raise DataCollectionError(f"Failed to collect options data: {str(e)}")
    
    def _create_option_data(self, symbol: str, option_data: Dict[str, Any], 
                          option_type: OptionType, expiry_date: datetime) -> OptionsData:
        """Create OptionsData object from Yahoo Finance data"""
        
        return OptionsData(
            symbol=symbol,
            expiry_date=expiry_date,
            strike=float(option_data.get("strike", 0)),
            option_type=option_type,
            last_price=float(option_data.get("lastPrice", 0)),
            bid_price=float(option_data.get("bid", 0)),
            ask_price=float(option_data.get("ask", 0)),
            volume=int(option_data.get("volume", 0)),
            open_interest=int(option_data.get("openInterest", 0)),
            bid_qty=int(option_data.get("bidSize", 0)),
            ask_qty=int(option_data.get("askSize", 0)),
            implied_volatility=float(option_data.get("impliedVolatility", 0)) if option_data.get("impliedVolatility") else None,
            timestamp=datetime.now(),
            change=float(option_data.get("change", 0)),
            percent_change=float(option_data.get("percentChange", 0))
        )
    
    async def collect_all_symbols(self, symbols: List[str]) -> Dict[str, List[OptionsData]]:
        """
        Collect options data for all symbols concurrently
        """
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(
                self.get_options_chain(symbol),
                name=f"yahoo_collect_{symbol}"
            )
            tasks.append((symbol, task))
        
        results = {}
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        for (symbol, _), result in zip(tasks, completed_tasks):
            if isinstance(result, Exception):
                logger.error(f"Failed to collect data for {symbol}: {str(result)}")
                results[symbol] = []
            else:
                results[symbol] = result
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get collector statistics"""
        error_rate = (self.error_count / max(self.request_count, 1)) * 100
        
        return {
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "error_rate": error_rate,
            "last_request_time": self.last_request_time.isoformat() if self.last_request_time else None,
            "session_active": self.session is not None
        }

# Global instance
yahoo_collector = YahooFinanceCollector()
