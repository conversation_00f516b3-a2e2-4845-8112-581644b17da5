"""
Volatility regime filtering to prevent trading during extreme market conditions
Implements statistical regime detection to avoid false positives during market stress
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import numpy as np
import pandas as pd
from enum import Enum
import statistics

from models.data_models import OptionsData

logger = logging.getLogger(__name__)

class VolatilityRegime(str, Enum):
    """Volatility regime classifications"""
    ULTRA_LOW = "ultra_low"      # VIX < 12, very calm markets
    LOW = "low"                  # VIX 12-16, normal calm markets
    NORMAL = "normal"            # VIX 16-25, typical market conditions
    ELEVATED = "elevated"        # VIX 25-35, heightened uncertainty
    HIGH = "high"                # VIX 35-50, stressed markets
    EXTREME = "extreme"          # VIX > 50, crisis conditions

class TrendRegime(str, Enum):
    """Trend regime classifications"""
    STRONG_UPTREND = "strong_uptrend"
    UPTREND = "uptrend"
    SIDEWAYS = "sideways"
    DOWNTREND = "downtrend"
    STRONG_DOWNTREND = "strong_downtrend"

@dataclass
class RegimeMetrics:
    """Market regime metrics"""
    volatility_regime: VolatilityRegime
    trend_regime: TrendRegime
    implied_volatility: float
    realized_volatility: float
    volume_regime: str
    price_momentum: float
    regime_confidence: float
    timestamp: datetime
    
    def should_allow_trading(self) -> bool:
        """Determine if trading should be allowed in this regime"""
        # Don't trade in extreme volatility
        if self.volatility_regime in [VolatilityRegime.EXTREME, VolatilityRegime.HIGH]:
            return False
        
        # Don't trade in strong trending markets (manipulation detection less reliable)
        if self.trend_regime in [TrendRegime.STRONG_UPTREND, TrendRegime.STRONG_DOWNTREND]:
            return False
        
        # Require minimum confidence in regime classification
        if self.regime_confidence < 0.6:
            return False
        
        return True

class VolatilityRegimeFilter:
    """
    Advanced volatility regime filter that prevents trading during market stress
    Uses multiple indicators to classify market conditions
    """
    
    def __init__(self, lookback_periods: int = 50):
        self.lookback_periods = lookback_periods
        self.price_history: deque = deque(maxlen=lookback_periods)
        self.volume_history: deque = deque(maxlen=lookback_periods)
        self.iv_history: deque = deque(maxlen=lookback_periods)
        self.regime_history: deque = deque(maxlen=20)
        
        self.current_regime: Optional[RegimeMetrics] = None
        
        # Volatility thresholds (based on historical VIX levels)
        self.volatility_thresholds = {
            VolatilityRegime.ULTRA_LOW: (0.0, 0.12),
            VolatilityRegime.LOW: (0.12, 0.16),
            VolatilityRegime.NORMAL: (0.16, 0.25),
            VolatilityRegime.ELEVATED: (0.25, 0.35),
            VolatilityRegime.HIGH: (0.35, 0.50),
            VolatilityRegime.EXTREME: (0.50, float('inf'))
        }
        
        # Trend detection parameters
        self.trend_lookback = 20
        self.trend_thresholds = {
            'strong_trend': 0.02,  # 2% move over lookback period
            'weak_trend': 0.005    # 0.5% move over lookback period
        }
    
    def update_market_data(self, options_data: List[OptionsData]) -> RegimeMetrics:
        """
        Update regime filter with new market data
        
        Args:
            options_data: Latest options market data
            
        Returns:
            Current regime metrics
        """
        if not options_data:
            return self.current_regime
        
        # Calculate market metrics
        market_metrics = self._calculate_market_metrics(options_data)
        
        # Store historical data
        self.price_history.append(market_metrics['avg_price'])
        self.volume_history.append(market_metrics['total_volume'])
        self.iv_history.append(market_metrics['avg_iv'])
        
        # Calculate regime metrics
        regime_metrics = self._calculate_regime_metrics(market_metrics)
        
        # Update current regime
        self.current_regime = regime_metrics
        self.regime_history.append(regime_metrics)
        
        # Log regime changes
        if (len(self.regime_history) > 1 and 
            self.regime_history[-1].volatility_regime != self.regime_history[-2].volatility_regime):
            logger.info(
                f"Volatility regime changed: {self.regime_history[-2].volatility_regime} -> "
                f"{regime_metrics.volatility_regime} (IV: {regime_metrics.implied_volatility:.1%})"
            )
        
        return regime_metrics
    
    def _calculate_market_metrics(self, options_data: List[OptionsData]) -> Dict[str, float]:
        """Calculate aggregate market metrics from options data"""
        if not options_data:
            return {}
        
        # Convert to arrays for efficient calculation
        prices = np.array([opt.last_price for opt in options_data if opt.last_price > 0])
        volumes = np.array([opt.volume for opt in options_data])
        ivs = np.array([opt.implied_volatility for opt in options_data if opt.implied_volatility and opt.implied_volatility > 0])
        
        metrics = {
            'avg_price': np.mean(prices) if len(prices) > 0 else 0,
            'price_std': np.std(prices) if len(prices) > 1 else 0,
            'total_volume': np.sum(volumes),
            'avg_volume': np.mean(volumes) if len(volumes) > 0 else 0,
            'volume_std': np.std(volumes) if len(volumes) > 1 else 0,
            'avg_iv': np.mean(ivs) if len(ivs) > 0 else 0,
            'iv_std': np.std(ivs) if len(ivs) > 1 else 0,
            'max_iv': np.max(ivs) if len(ivs) > 0 else 0,
            'min_iv': np.min(ivs) if len(ivs) > 0 else 0
        }
        
        return metrics
    
    def _calculate_regime_metrics(self, market_metrics: Dict[str, float]) -> RegimeMetrics:
        """Calculate comprehensive regime metrics"""
        
        # 1. Volatility Regime
        avg_iv = market_metrics.get('avg_iv', 0)
        volatility_regime = self._classify_volatility_regime(avg_iv)
        
        # 2. Trend Regime
        trend_regime = self._classify_trend_regime()
        
        # 3. Realized Volatility
        realized_vol = self._calculate_realized_volatility()
        
        # 4. Volume Regime
        volume_regime = self._classify_volume_regime(market_metrics.get('total_volume', 0))
        
        # 5. Price Momentum
        price_momentum = self._calculate_price_momentum()
        
        # 6. Regime Confidence
        regime_confidence = self._calculate_regime_confidence(
            volatility_regime, trend_regime, avg_iv, realized_vol
        )
        
        return RegimeMetrics(
            volatility_regime=volatility_regime,
            trend_regime=trend_regime,
            implied_volatility=avg_iv,
            realized_volatility=realized_vol,
            volume_regime=volume_regime,
            price_momentum=price_momentum,
            regime_confidence=regime_confidence,
            timestamp=datetime.now()
        )
    
    def _classify_volatility_regime(self, implied_volatility: float) -> VolatilityRegime:
        """Classify volatility regime based on implied volatility"""
        for regime, (low, high) in self.volatility_thresholds.items():
            if low <= implied_volatility < high:
                return regime
        
        return VolatilityRegime.NORMAL  # Default fallback
    
    def _classify_trend_regime(self) -> TrendRegime:
        """Classify trend regime based on price momentum"""
        if len(self.price_history) < self.trend_lookback:
            return TrendRegime.SIDEWAYS
        
        prices = list(self.price_history)[-self.trend_lookback:]
        
        # Calculate trend strength
        start_price = prices[0]
        end_price = prices[-1]
        
        if start_price <= 0:
            return TrendRegime.SIDEWAYS
        
        trend_strength = (end_price - start_price) / start_price
        
        # Classify trend
        if trend_strength > self.trend_thresholds['strong_trend']:
            return TrendRegime.STRONG_UPTREND
        elif trend_strength > self.trend_thresholds['weak_trend']:
            return TrendRegime.UPTREND
        elif trend_strength < -self.trend_thresholds['strong_trend']:
            return TrendRegime.STRONG_DOWNTREND
        elif trend_strength < -self.trend_thresholds['weak_trend']:
            return TrendRegime.DOWNTREND
        else:
            return TrendRegime.SIDEWAYS
    
    def _calculate_realized_volatility(self) -> float:
        """Calculate realized volatility from price history"""
        if len(self.price_history) < 10:
            return 0.0
        
        prices = np.array(list(self.price_history))
        returns = np.diff(np.log(prices[prices > 0]))
        
        if len(returns) < 2:
            return 0.0
        
        # Annualized realized volatility
        return np.std(returns) * np.sqrt(252)  # 252 trading days
    
    def _classify_volume_regime(self, current_volume: float) -> str:
        """Classify volume regime"""
        if len(self.volume_history) < 10:
            return "normal"
        
        volumes = list(self.volume_history)
        avg_volume = np.mean(volumes)
        
        if current_volume > avg_volume * 2:
            return "high"
        elif current_volume > avg_volume * 1.5:
            return "elevated"
        elif current_volume < avg_volume * 0.5:
            return "low"
        else:
            return "normal"
    
    def _calculate_price_momentum(self) -> float:
        """Calculate price momentum over short term"""
        if len(self.price_history) < 5:
            return 0.0
        
        prices = list(self.price_history)
        recent_prices = prices[-5:]
        
        if recent_prices[0] <= 0:
            return 0.0
        
        return (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
    
    def _calculate_regime_confidence(
        self, 
        vol_regime: VolatilityRegime, 
        trend_regime: TrendRegime,
        implied_vol: float,
        realized_vol: float
    ) -> float:
        """Calculate confidence in regime classification"""
        confidence = 0.5  # Base confidence
        
        # Higher confidence if IV and RV are aligned
        if abs(implied_vol - realized_vol) < 0.05:  # Within 5%
            confidence += 0.2
        
        # Higher confidence for extreme regimes (easier to classify)
        if vol_regime in [VolatilityRegime.ULTRA_LOW, VolatilityRegime.EXTREME]:
            confidence += 0.2
        
        # Higher confidence for strong trends
        if trend_regime in [TrendRegime.STRONG_UPTREND, TrendRegime.STRONG_DOWNTREND]:
            confidence += 0.1
        
        # Lower confidence if we have insufficient data
        if len(self.price_history) < self.lookback_periods // 2:
            confidence -= 0.2
        
        return max(0.0, min(1.0, confidence))
    
    def should_allow_trading(self) -> Tuple[bool, str]:
        """
        Check if trading should be allowed in current regime
        
        Returns:
            Tuple of (should_trade, reason)
        """
        if not self.current_regime:
            return False, "No regime data available"
        
        if not self.current_regime.should_allow_trading():
            reasons = []
            
            if self.current_regime.volatility_regime in [VolatilityRegime.EXTREME, VolatilityRegime.HIGH]:
                reasons.append(f"High volatility ({self.current_regime.volatility_regime})")
            
            if self.current_regime.trend_regime in [TrendRegime.STRONG_UPTREND, TrendRegime.STRONG_DOWNTREND]:
                reasons.append(f"Strong trend ({self.current_regime.trend_regime})")
            
            if self.current_regime.regime_confidence < 0.6:
                reasons.append(f"Low confidence ({self.current_regime.regime_confidence:.1%})")
            
            return False, "; ".join(reasons)
        
        return True, f"Regime OK ({self.current_regime.volatility_regime}, {self.current_regime.trend_regime})"
    
    def get_regime_summary(self) -> Dict[str, Any]:
        """Get comprehensive regime summary"""
        if not self.current_regime:
            return {"status": "no_data"}
        
        return {
            "volatility_regime": self.current_regime.volatility_regime,
            "trend_regime": self.current_regime.trend_regime,
            "implied_volatility": self.current_regime.implied_volatility,
            "realized_volatility": self.current_regime.realized_volatility,
            "volume_regime": self.current_regime.volume_regime,
            "price_momentum": self.current_regime.price_momentum,
            "regime_confidence": self.current_regime.regime_confidence,
            "should_trade": self.current_regime.should_allow_trading(),
            "last_update": self.current_regime.timestamp,
            "data_points": len(self.price_history)
        }
    
    def get_regime_history(self, periods: int = 10) -> List[Dict[str, Any]]:
        """Get recent regime history"""
        recent_regimes = list(self.regime_history)[-periods:]
        
        return [{
            "timestamp": regime.timestamp,
            "volatility_regime": regime.volatility_regime,
            "trend_regime": regime.trend_regime,
            "implied_volatility": regime.implied_volatility,
            "should_trade": regime.should_allow_trading()
        } for regime in recent_regimes]

# Global volatility regime filter
volatility_regime_filter = VolatilityRegimeFilter()
