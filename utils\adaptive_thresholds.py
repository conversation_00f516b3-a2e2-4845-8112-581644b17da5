"""
Dynamic threshold adaptation system that evolves with market conditions
Replaces static thresholds with percentile-based adaptive thresholds
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import numpy as np
import pandas as pd
from enum import Enum
import statistics

from models.data_models import OptionsData

logger = logging.getLogger(__name__)

class MarketRegime(str, Enum):
    """Market regime classifications"""
    LOW_VOLATILITY = "low_vol"
    NORMAL = "normal"
    HIGH_VOLATILITY = "high_vol"
    EXTREME_VOLATILITY = "extreme_vol"
    TRENDING = "trending"
    RANGING = "ranging"

@dataclass
class ThresholdConfig:
    """Configuration for adaptive threshold calculation"""
    lookback_periods: int = 100  # Number of periods to look back
    percentile_low: float = 10.0  # Lower percentile for threshold
    percentile_high: float = 90.0  # Upper percentile for threshold
    min_threshold: float = 0.0  # Minimum threshold value
    max_threshold: float = float('inf')  # Maximum threshold value
    adaptation_rate: float = 0.1  # How quickly to adapt (0.0 = no adaptation, 1.0 = instant)

@dataclass
class AdaptiveThreshold:
    """Adaptive threshold that evolves with market conditions"""
    name: str
    config: ThresholdConfig
    historical_values: deque = field(default_factory=lambda: deque(maxlen=1000))
    current_threshold: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)
    regime_multipliers: Dict[MarketRegime, float] = field(default_factory=lambda: {
        MarketRegime.LOW_VOLATILITY: 0.5,
        MarketRegime.NORMAL: 1.0,
        MarketRegime.HIGH_VOLATILITY: 1.5,
        MarketRegime.EXTREME_VOLATILITY: 2.0,
        MarketRegime.TRENDING: 1.2,
        MarketRegime.RANGING: 0.8
    })

class AdaptiveThresholdManager:
    """
    Manages adaptive thresholds that evolve with market conditions
    Replaces static thresholds with data-driven, regime-aware thresholds
    """
    
    def __init__(self):
        self.thresholds: Dict[str, AdaptiveThreshold] = {}
        self.market_data_history: deque = deque(maxlen=1000)
        self.current_regime = MarketRegime.NORMAL
        self.regime_history: deque = deque(maxlen=100)
        
        # Initialize default thresholds
        self._initialize_default_thresholds()
    
    def _initialize_default_thresholds(self):
        """Initialize default adaptive thresholds for manipulation detection"""
        
        # Spoofing detection thresholds
        self.register_threshold(
            "spoof_qty_threshold",
            ThresholdConfig(
                lookback_periods=50,
                percentile_low=75.0,  # Use 75th percentile as threshold
                percentile_high=95.0,
                min_threshold=100.0,  # Minimum 100 lots
                max_threshold=50000.0,  # Maximum 50k lots
                adaptation_rate=0.2
            )
        )
        
        self.register_threshold(
            "spoof_price_impact_threshold",
            ThresholdConfig(
                lookback_periods=100,
                percentile_low=80.0,  # Use 80th percentile
                percentile_high=95.0,
                min_threshold=0.001,  # Minimum 0.1% price impact
                max_threshold=0.1,    # Maximum 10% price impact
                adaptation_rate=0.15
            )
        )
        
        # Volume-based thresholds
        self.register_threshold(
            "volume_spike_threshold",
            ThresholdConfig(
                lookback_periods=30,
                percentile_low=90.0,  # Use 90th percentile for volume spikes
                percentile_high=99.0,
                min_threshold=1000.0,
                max_threshold=1000000.0,
                adaptation_rate=0.25
            )
        )
        
        # Volatility thresholds
        self.register_threshold(
            "volatility_threshold",
            ThresholdConfig(
                lookback_periods=20,
                percentile_low=70.0,
                percentile_high=95.0,
                min_threshold=0.05,   # 5% minimum volatility
                max_threshold=2.0,    # 200% maximum volatility
                adaptation_rate=0.1
            )
        )
    
    def register_threshold(self, name: str, config: ThresholdConfig):
        """Register a new adaptive threshold"""
        self.thresholds[name] = AdaptiveThreshold(
            name=name,
            config=config,
            current_threshold=config.min_threshold
        )
        logger.info(f"Registered adaptive threshold: {name}")
    
    def update_market_data(self, options_data: List[OptionsData]):
        """
        Update market data and recalculate thresholds
        
        Args:
            options_data: Latest market data
        """
        if not options_data:
            return
        
        # Store market data
        self.market_data_history.append({
            'timestamp': datetime.now(),
            'data': options_data,
            'metrics': self._calculate_market_metrics(options_data)
        })
        
        # Update market regime
        self._update_market_regime()
        
        # Update all thresholds
        self._update_all_thresholds()
    
    def _calculate_market_metrics(self, options_data: List[OptionsData]) -> Dict[str, float]:
        """Calculate market metrics for threshold adaptation"""
        if not options_data:
            return {}
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame([{
            'symbol': opt.symbol,
            'strike': opt.strike,
            'last_price': opt.last_price,
            'volume': opt.volume,
            'bid_qty': opt.bid_qty,
            'ask_qty': opt.ask_qty,
            'open_interest': opt.open_interest,
            'implied_volatility': opt.implied_volatility or 0.0
        } for opt in options_data])
        
        if df.empty:
            return {}
        
        metrics = {
            'avg_volume': df['volume'].mean(),
            'max_volume': df['volume'].max(),
            'volume_std': df['volume'].std(),
            'avg_bid_qty': df['bid_qty'].mean(),
            'max_bid_qty': df['bid_qty'].max(),
            'avg_ask_qty': df['ask_qty'].mean(),
            'max_ask_qty': df['ask_qty'].max(),
            'avg_iv': df['implied_volatility'].mean(),
            'iv_std': df['implied_volatility'].std(),
            'price_range': df['last_price'].max() - df['last_price'].min(),
            'total_volume': df['volume'].sum(),
            'total_oi': df['open_interest'].sum()
        }
        
        return metrics
    
    def _update_market_regime(self):
        """Update current market regime based on recent data"""
        if len(self.market_data_history) < 10:
            return
        
        # Get recent metrics
        recent_metrics = [entry['metrics'] for entry in list(self.market_data_history)[-10:]]
        
        # Calculate volatility indicators
        iv_values = [m.get('avg_iv', 0) for m in recent_metrics if m.get('avg_iv')]
        volume_values = [m.get('avg_volume', 0) for m in recent_metrics if m.get('avg_volume')]
        
        if not iv_values or not volume_values:
            return
        
        current_iv = iv_values[-1]
        iv_std = np.std(iv_values) if len(iv_values) > 1 else 0
        volume_trend = np.mean(volume_values[-3:]) / np.mean(volume_values[-10:]) if len(volume_values) >= 10 else 1.0
        
        # Determine regime
        if current_iv > 0.5:  # Very high IV
            new_regime = MarketRegime.EXTREME_VOLATILITY
        elif current_iv > 0.3:  # High IV
            new_regime = MarketRegime.HIGH_VOLATILITY
        elif current_iv < 0.1:  # Low IV
            new_regime = MarketRegime.LOW_VOLATILITY
        else:
            new_regime = MarketRegime.NORMAL
        
        # Check for trending vs ranging
        if volume_trend > 1.5:  # High volume suggests trending
            if new_regime == MarketRegime.NORMAL:
                new_regime = MarketRegime.TRENDING
        elif volume_trend < 0.7:  # Low volume suggests ranging
            if new_regime == MarketRegime.NORMAL:
                new_regime = MarketRegime.RANGING
        
        if new_regime != self.current_regime:
            logger.info(f"Market regime changed: {self.current_regime} -> {new_regime}")
            self.current_regime = new_regime
            self.regime_history.append({
                'regime': new_regime,
                'timestamp': datetime.now(),
                'iv': current_iv,
                'volume_trend': volume_trend
            })
    
    def _update_all_thresholds(self):
        """Update all registered thresholds"""
        for threshold in self.thresholds.values():
            self._update_threshold(threshold)
    
    def _update_threshold(self, threshold: AdaptiveThreshold):
        """Update a specific threshold based on recent data"""
        if len(self.market_data_history) < threshold.config.lookback_periods:
            return
        
        # Get relevant historical values based on threshold type
        historical_values = self._extract_threshold_values(threshold.name)
        
        if not historical_values:
            return
        
        # Calculate percentile-based threshold
        base_threshold = np.percentile(historical_values, threshold.config.percentile_low)
        
        # Apply regime multiplier
        regime_multiplier = threshold.regime_multipliers.get(self.current_regime, 1.0)
        adjusted_threshold = base_threshold * regime_multiplier
        
        # Apply bounds
        adjusted_threshold = max(threshold.config.min_threshold, 
                               min(threshold.config.max_threshold, adjusted_threshold))
        
        # Apply adaptation rate (smooth the change)
        if threshold.current_threshold > 0:
            new_threshold = (threshold.current_threshold * (1 - threshold.config.adaptation_rate) + 
                           adjusted_threshold * threshold.config.adaptation_rate)
        else:
            new_threshold = adjusted_threshold
        
        # Update threshold
        old_threshold = threshold.current_threshold
        threshold.current_threshold = new_threshold
        threshold.last_update = datetime.now()
        
        # Log significant changes
        if abs(new_threshold - old_threshold) / max(old_threshold, 1) > 0.2:  # 20% change
            logger.info(
                f"Threshold {threshold.name} adapted: {old_threshold:.2f} -> {new_threshold:.2f} "
                f"(regime: {self.current_regime}, multiplier: {regime_multiplier:.2f})"
            )
    
    def _extract_threshold_values(self, threshold_name: str) -> List[float]:
        """Extract relevant values for threshold calculation"""
        values = []
        
        for entry in self.market_data_history:
            metrics = entry['metrics']
            
            if threshold_name == "spoof_qty_threshold":
                values.extend([metrics.get('max_bid_qty', 0), metrics.get('max_ask_qty', 0)])
            elif threshold_name == "spoof_price_impact_threshold":
                # Use price range as proxy for price impact
                avg_price = np.mean([opt.last_price for opt in entry['data']])
                if avg_price > 0:
                    price_impact = metrics.get('price_range', 0) / avg_price
                    values.append(price_impact)
            elif threshold_name == "volume_spike_threshold":
                values.append(metrics.get('max_volume', 0))
            elif threshold_name == "volatility_threshold":
                values.append(metrics.get('avg_iv', 0))
        
        return [v for v in values if v > 0]  # Filter out zero values
    
    def get_threshold(self, name: str) -> float:
        """Get current threshold value"""
        if name not in self.thresholds:
            logger.warning(f"Unknown threshold: {name}")
            return 0.0
        
        return self.thresholds[name].current_threshold
    
    def get_all_thresholds(self) -> Dict[str, float]:
        """Get all current threshold values"""
        return {name: threshold.current_threshold for name, threshold in self.thresholds.items()}
    
    def get_threshold_stats(self, name: str) -> Dict[str, Any]:
        """Get detailed statistics for a threshold"""
        if name not in self.thresholds:
            return {}
        
        threshold = self.thresholds[name]
        historical_values = self._extract_threshold_values(name)
        
        stats = {
            'current_value': threshold.current_threshold,
            'last_update': threshold.last_update,
            'regime': self.current_regime,
            'regime_multiplier': threshold.regime_multipliers.get(self.current_regime, 1.0),
            'config': {
                'lookback_periods': threshold.config.lookback_periods,
                'percentile_low': threshold.config.percentile_low,
                'min_threshold': threshold.config.min_threshold,
                'max_threshold': threshold.config.max_threshold
            }
        }
        
        if historical_values:
            stats['historical_stats'] = {
                'count': len(historical_values),
                'mean': np.mean(historical_values),
                'std': np.std(historical_values),
                'min': np.min(historical_values),
                'max': np.max(historical_values),
                'p10': np.percentile(historical_values, 10),
                'p50': np.percentile(historical_values, 50),
                'p90': np.percentile(historical_values, 90)
            }
        
        return stats
    
    def should_trade_in_regime(self) -> bool:
        """Check if current regime allows trading"""
        # Don't trade in extreme volatility
        if self.current_regime == MarketRegime.EXTREME_VOLATILITY:
            return False
        
        return True
    
    def get_regime_info(self) -> Dict[str, Any]:
        """Get current market regime information"""
        return {
            'current_regime': self.current_regime,
            'regime_history': list(self.regime_history)[-10:],  # Last 10 regime changes
            'should_trade': self.should_trade_in_regime()
        }

# Global adaptive threshold manager
adaptive_threshold_manager = AdaptiveThresholdManager()
