# Environment Configuration for Options Manipulation Detection System

# Environment
ENVIRONMENT=development
DEBUG=true

# Database Configuration
DB_URL=postgresql://options_user:options_password@localhost:5432/options_db
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# NSE API Configuration
NSE_BASE_URL=https://www.nseindia.com/api
NSE_OPTIONS_ENDPOINT=/option-chain-indices
NSE_HISTORICAL_ENDPOINT=/historical/cm/equity
NSE_MARKET_STATUS_ENDPOINT=/marketStatus
NSE_REQUESTS_PER_MINUTE=60
NSE_REQUESTS_PER_SECOND=2

# Optional API Keys (for premium data providers as backup)
NSE_API_KEY=
NSE_API_SECRET=

# Detection Algorithm Configuration
DETECTION_SPOOF_QTY_THRESHOLD=1000
DETECTION_SPOOF_TIME_WINDOW_SECONDS=30
DETECTION_GAMMA_EXPOSURE_THRESHOLD=1000000.0
DETECTION_GAMMA_VOLUME_RATIO=0.5
DETECTION_IV_CHANGE_THRESHOLD=0.05
DETECTION_VOLUME_PERCENTILE_THRESHOLD=0.8
DETECTION_SPREAD_ZSCORE_THRESHOLD=2.0
DETECTION_CORRELATION_WINDOW=10
DETECTION_HIGH_CONFIDENCE_THRESHOLD=0.8
DETECTION_MEDIUM_CONFIDENCE_THRESHOLD=0.6

# Monitoring Configuration
MONITORING_PROMETHEUS_PORT=8000
MONITORING_LOG_LEVEL=INFO
MONITORING_SENTRY_DSN=
MONITORING_ALERT_CONFIDENCE_THRESHOLD=0.8
MONITORING_ALERT_PROFIT_THRESHOLD=100000.0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8080
API_WORKERS=4
API_RELOAD=false

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=Asia/Kolkata

# System Configuration
SYMBOLS=["NIFTY", "BANKNIFTY", "FINNIFTY"]
DATA_COLLECTION_INTERVAL=30
DETECTION_INTERVAL=60

# Security (for production)
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Email Configuration (for alerts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# Slack Configuration (for alerts)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Telegram Configuration (for alerts)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id
