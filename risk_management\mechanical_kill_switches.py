"""
Mechanical Kill Switches
The Architect's Fix #6: Create Mechanical Kill Switches

Quantitative stop conditions: >15% drawdown OR 7 consecutive losses
OR confidence-weighted win rate <40% over 30 trades.
Make it mechanical, not emotional.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class KillSwitchType(Enum):
    """Types of kill switches"""
    DRAWDOWN = "drawdown"
    CONSECUTIVE_LOSSES = "consecutive_losses"
    WIN_RATE = "win_rate"
    CONFIDENCE_DEGRADATION = "confidence_degradation"
    MANUAL_OVERRIDE = "manual_override"

@dataclass
class TradeResult:
    """Individual trade result"""
    timestamp: datetime
    symbol: str
    entry_price: float
    exit_price: float
    quantity: int
    pnl: float
    confidence: float
    was_winner: bool

@dataclass
class KillSwitchStatus:
    """Kill switch status"""
    is_triggered: bool
    trigger_type: Optional[KillSwitchType]
    trigger_reason: str
    current_drawdown: float
    consecutive_losses: int
    win_rate_30_trades: float
    confidence_weighted_win_rate: float
    trades_analyzed: int
    reset_required: bool

class MechanicalKillSwitches:
    """
    Mechanical trading halt system
    No emotions, no hope, no "just one more trade"
    """

    def __init__(self, config: Dict = None):
        self.config = config or {}

        # Kill switch thresholds
        self.max_drawdown_pct = self.config.get('max_drawdown_pct', 15.0)  # 15%
        self.max_consecutive_losses = self.config.get('max_consecutive_losses', 7)
        self.min_win_rate_30_trades = self.config.get('min_win_rate_30_trades', 40.0)  # 40%
        self.min_confidence_weighted_win_rate = self.config.get('min_confidence_weighted_win_rate', 35.0)  # 35%

        # Analysis parameters
        self.min_trades_for_analysis = self.config.get('min_trades_for_analysis', 30)
        self.lookback_days = self.config.get('lookback_days', 90)

        # State tracking
        self.trade_history: List[TradeResult] = []
        self.peak_equity = 0.0
        self.current_equity = 0.0
        self.is_killed = False
        self.kill_reason = ""
        self.kill_timestamp: Optional[datetime] = None

    def add_trade_result(self, trade: TradeResult):
        """
        Add new trade result and check kill switches

        Args:
            trade: Trade result to add
        """
        self.trade_history.append(trade)

        # Update equity tracking
        self.current_equity += trade.pnl
        if self.current_equity > self.peak_equity:
            self.peak_equity = self.current_equity

        # Check kill switches
        self._check_all_kill_switches()

        # Clean old trades
        self._clean_old_trades()

    def get_kill_switch_status(self) -> KillSwitchStatus:
        """
        Get current kill switch status

        Returns:
            Current kill switch status
        """
        if self.is_killed:
            return KillSwitchStatus(
                is_triggered=True,
                trigger_type=self._get_trigger_type(),
                trigger_reason=self.kill_reason,
                current_drawdown=self._calculate_current_drawdown(),
                consecutive_losses=self._count_consecutive_losses(),
                win_rate_30_trades=self._calculate_win_rate_30_trades(),
                confidence_weighted_win_rate=self._calculate_confidence_weighted_win_rate(),
                trades_analyzed=len(self.trade_history),
                reset_required=True
            )

        return KillSwitchStatus(
            is_triggered=False,
            trigger_type=None,
            trigger_reason="All systems operational",
            current_drawdown=self._calculate_current_drawdown(),
            consecutive_losses=self._count_consecutive_losses(),
            win_rate_30_trades=self._calculate_win_rate_30_trades(),
            confidence_weighted_win_rate=self._calculate_confidence_weighted_win_rate(),
            trades_analyzed=len(self.trade_history),
            reset_required=False
        )

    def should_allow_trading(self) -> bool:
        """
        Check if trading should be allowed

        Returns:
            True if trading allowed, False if kill switch triggered
        """
        return not self.is_killed

    def manual_kill_switch(self, reason: str = "Manual override"):
        """
        Manually trigger kill switch

        Args:
            reason: Reason for manual kill
        """
        self.is_killed = True
        self.kill_reason = f"MANUAL KILL: {reason}"
        self.kill_timestamp = datetime.now()

        logger.critical(f"MANUAL KILL SWITCH ACTIVATED: {reason}")

    def reset_kill_switches(self, reason: str = "Manual reset"):
        """
        Reset kill switches (use with extreme caution)

        Args:
            reason: Reason for reset
        """
        logger.warning(f"KILL SWITCH RESET: {reason}")

        self.is_killed = False
        self.kill_reason = ""
        self.kill_timestamp = None

        # Reset equity tracking to current level
        self.peak_equity = self.current_equity

    def _check_all_kill_switches(self):
        """Check all kill switch conditions"""
        if self.is_killed:
            return  # Already killed

        # Check drawdown kill switch
        if self._check_drawdown_kill_switch():
            return

        # Check consecutive losses kill switch
        if self._check_consecutive_losses_kill_switch():
            return

        # Check win rate kill switch (only if enough trades)
        if len(self.trade_history) >= self.min_trades_for_analysis:
            if self._check_win_rate_kill_switch():
                return

            if self._check_confidence_weighted_win_rate_kill_switch():
                return

    def _check_drawdown_kill_switch(self) -> bool:
        """
        Check drawdown kill switch

        Returns:
            True if kill switch triggered
        """
        current_drawdown = self._calculate_current_drawdown()

        if current_drawdown >= self.max_drawdown_pct:
            self._trigger_kill_switch(
                KillSwitchType.DRAWDOWN,
                f"Drawdown exceeded {self.max_drawdown_pct}%: {current_drawdown:.1f}%"
            )
            return True

        return False

    def _check_consecutive_losses_kill_switch(self) -> bool:
        """
        Check consecutive losses kill switch

        Returns:
            True if kill switch triggered
        """
        consecutive_losses = self._count_consecutive_losses()

        if consecutive_losses >= self.max_consecutive_losses:
            self._trigger_kill_switch(
                KillSwitchType.CONSECUTIVE_LOSSES,
                f"Consecutive losses exceeded {self.max_consecutive_losses}: {consecutive_losses}"
            )
            return True

        return False

    def _check_win_rate_kill_switch(self) -> bool:
        """
        Check win rate kill switch

        Returns:
            True if kill switch triggered
        """
        win_rate = self._calculate_win_rate_30_trades()

        if win_rate < self.min_win_rate_30_trades:
            self._trigger_kill_switch(
                KillSwitchType.WIN_RATE,
                f"Win rate below {self.min_win_rate_30_trades}%: {win_rate:.1f}%"
            )
            return True

        return False

    def _check_confidence_weighted_win_rate_kill_switch(self) -> bool:
        """
        Check confidence-weighted win rate kill switch

        Returns:
            True if kill switch triggered
        """
        confidence_win_rate = self._calculate_confidence_weighted_win_rate()

        if confidence_win_rate < self.min_confidence_weighted_win_rate:
            self._trigger_kill_switch(
                KillSwitchType.CONFIDENCE_DEGRADATION,
                f"Confidence-weighted win rate below {self.min_confidence_weighted_win_rate}%: {confidence_win_rate:.1f}%"
            )
            return True

        return False

    def _trigger_kill_switch(self, switch_type: KillSwitchType, reason: str):
        """
        Trigger kill switch

        Args:
            switch_type: Type of kill switch
            reason: Reason for trigger
        """
        self.is_killed = True
        self.kill_reason = f"{switch_type.value.upper()}: {reason}"
        self.kill_timestamp = datetime.now()

        logger.critical(f"🚨 KILL SWITCH ACTIVATED 🚨")
        logger.critical(f"Type: {switch_type.value}")
        logger.critical(f"Reason: {reason}")
        logger.critical(f"Time: {self.kill_timestamp}")
        logger.critical(f"ALL TRADING HALTED")

    def _calculate_current_drawdown(self) -> float:
        """
        Calculate current drawdown percentage

        Returns:
            Current drawdown as percentage
        """
        if self.peak_equity <= 0:
            return 0.0

        drawdown = (self.peak_equity - self.current_equity) / self.peak_equity * 100
        return max(drawdown, 0.0)

    def _count_consecutive_losses(self) -> int:
        """
        Count consecutive losses from most recent trades

        Returns:
            Number of consecutive losses
        """
        if not self.trade_history:
            return 0

        consecutive = 0
        for trade in reversed(self.trade_history):
            if not trade.was_winner:
                consecutive += 1
            else:
                break

        return consecutive

    def _calculate_win_rate_30_trades(self) -> float:
        """
        Calculate win rate over last 30 trades

        Returns:
            Win rate as percentage
        """
        if len(self.trade_history) < self.min_trades_for_analysis:
            return 100.0  # Assume good until proven otherwise

        recent_trades = self.trade_history[-30:]
        winners = sum(1 for trade in recent_trades if trade.was_winner)

        return (winners / len(recent_trades)) * 100

    def _calculate_confidence_weighted_win_rate(self) -> float:
        """
        Calculate confidence-weighted win rate over last 30 trades

        Returns:
            Confidence-weighted win rate as percentage
        """
        if len(self.trade_history) < self.min_trades_for_analysis:
            return 100.0  # Assume good until proven otherwise

        recent_trades = self.trade_history[-30:]

        total_confidence = sum(trade.confidence for trade in recent_trades)
        weighted_wins = sum(
            trade.confidence for trade in recent_trades
            if trade.was_winner
        )

        if total_confidence == 0:
            return 0.0

        return (weighted_wins / total_confidence) * 100

    def _get_trigger_type(self) -> Optional[KillSwitchType]:
        """
        Extract trigger type from kill reason

        Returns:
            KillSwitchType if killed, None otherwise
        """
        if not self.is_killed:
            return None

        reason_lower = self.kill_reason.lower()

        if "drawdown" in reason_lower:
            return KillSwitchType.DRAWDOWN
        elif "consecutive" in reason_lower:
            return KillSwitchType.CONSECUTIVE_LOSSES
        elif "win rate" in reason_lower and "confidence" not in reason_lower:
            return KillSwitchType.WIN_RATE
        elif "confidence" in reason_lower:
            return KillSwitchType.CONFIDENCE_DEGRADATION
        elif "manual" in reason_lower:
            return KillSwitchType.MANUAL_OVERRIDE
        else:
            return KillSwitchType.MANUAL_OVERRIDE

    def _clean_old_trades(self):
        """Remove trades older than lookback period"""
        cutoff_date = datetime.now() - timedelta(days=self.lookback_days)
        self.trade_history = [
            trade for trade in self.trade_history
            if trade.timestamp >= cutoff_date
        ]

    def get_performance_summary(self) -> Dict:
        """
        Get performance summary for analysis

        Returns:
            Performance metrics dictionary
        """
        if not self.trade_history:
            return {}

        total_pnl = sum(trade.pnl for trade in self.trade_history)
        winners = [trade for trade in self.trade_history if trade.was_winner]
        losers = [trade for trade in self.trade_history if not trade.was_winner]

        return {
            "total_trades": len(self.trade_history),
            "total_pnl": total_pnl,
            "current_equity": self.current_equity,
            "peak_equity": self.peak_equity,
            "current_drawdown_pct": self._calculate_current_drawdown(),
            "win_rate_pct": len(winners) / len(self.trade_history) * 100 if self.trade_history else 0,
            "avg_winner": np.mean([trade.pnl for trade in winners]) if winners else 0,
            "avg_loser": np.mean([trade.pnl for trade in losers]) if losers else 0,
            "consecutive_losses": self._count_consecutive_losses(),
            "confidence_weighted_win_rate": self._calculate_confidence_weighted_win_rate(),
            "is_killed": self.is_killed,
            "kill_reason": self.kill_reason,
            "kill_timestamp": self.kill_timestamp
        }