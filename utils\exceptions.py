"""
Custom exceptions for the Options Manipulation Detection System
"""

class OptionsDetectionError(Exception):
    """Base exception for options detection system"""
    pass

class DataCollectionError(OptionsDetectionError):
    """Raised when data collection fails"""
    pass

class RateLimitError(DataCollectionError):
    """Raised when API rate limits are exceeded"""
    pass

class DetectionError(OptionsDetectionError):
    """Raised when detection algorithms fail"""
    pass

class DatabaseError(OptionsDetectionError):
    """Raised when database operations fail"""
    pass

class CacheError(OptionsDetectionError):
    """Raised when cache operations fail"""
    pass

class ConfigurationError(OptionsDetectionError):
    """Raised when configuration is invalid"""
    pass

class AlertError(OptionsDetectionError):
    """Raised when alert system fails"""
    pass
