# Options Manipulation Detection & Front-Running System

A production-ready system for detecting institutional flow in Indian options markets and executing front-running strategies with sophisticated risk management. Built based on <PERSON> Architect's critique to address real market conditions.

## 🎯 System Overview

This system monitors NSE options data in real-time to detect spoofing patterns that reveal institutional flow direction. It implements a front-running strategy that trades WITH institutional flow rather than against manipulation patterns, using ultra-conservative risk management and realistic execution modeling.

## ✅ Key Features (The Architect's Fixes Implemented)

### Front-Running Strategy
- **Institutional Flow Detection**: Identifies real institutional flow hidden behind spoofing patterns
- **Directional Flow Analysis**: Bid spoofing → Bearish flow, Ask spoofing → Bullish flow
- **Flow Strength Calculation**: Only trades on strong institutional signals (>30% strength)
- **Front-Running Execution**: Trades WITH the institutions, not against the spoof

### Ultra-Conservative Risk Management
- **0.5% Risk Per Trade**: Maximum position size as demanded by The Architect
- **Multiple Risk Validation**: 8 risk checks before execution
- **Daily Trade Limits**: Maximum 10 trades per day to prevent overtrading
- **Liquidity Requirements**: Minimum volume thresholds and spread limits
- **Breakeven Win Rate**: Realistic 80%+ win rate calculation for front-running

### Realistic Execution Modeling
- **NSE Spread Simulation**: Actual bid-ask spreads based on market conditions
- **Partial Fills & Rejections**: 70% full fill, 20% partial, 10% rejection rates
- **Market Impact Modeling**: Slippage scales with order size and liquidity
- **Execution Delays**: 50-1000ms realistic execution times
- **Front-Running Penalties**: Additional 0.2% slippage for speed requirements

### End-to-End Latency Tracking
- **Complete Pipeline Measurement**: Market data → Signal → Order submission
- **API Latency Tracking**: NSE API calls, data processing, execution delays
- **Critical Path Analysis**: Identifies bottlenecks and optimization opportunities
- **Realistic Thresholds**: 800ms total pipeline (not fantasy 100ms)

### Production Architecture
- **Async Processing**: High-performance async data collection and processing
- **Caching Layer**: Redis-based caching for improved performance
- **Database**: PostgreSQL for reliable data storage with connection pooling
- **REST API**: FastAPI-based API for system interaction and monitoring
- **Comprehensive Testing**: All Architect fixes validated with test suite

## Quick Start

### Prerequisites
- Python 3.11+
- Docker and Docker Compose
- PostgreSQL 15+
- Redis 7+

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd options-manipulation-detection
```

2. **Set up environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Start with Docker Compose**
```bash
docker-compose up -d
```

### Manual Setup

1. **Start PostgreSQL and Redis**
```bash
# PostgreSQL
docker run -d --name postgres \
  -e POSTGRES_DB=options_db \
  -e POSTGRES_USER=options_user \
  -e POSTGRES_PASSWORD=options_password \
  -p 5432:5432 postgres:15-alpine

# Redis
docker run -d --name redis -p 6379:6379 redis:7-alpine
```

2. **Run the system**
```bash
# Full system (detection + API)
python main.py --mode full

# Detection only
python main.py --mode detection

# API only
python main.py --mode api
```

## Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database
DB_URL=postgresql://user:password@localhost:5432/options_db

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Detection thresholds
DETECTION_SPOOF_QTY_THRESHOLD=1000
DETECTION_GAMMA_EXPOSURE_THRESHOLD=1000000.0
DETECTION_HIGH_CONFIDENCE_THRESHOLD=0.8

# Monitoring
MONITORING_LOG_LEVEL=INFO
MONITORING_PROMETHEUS_PORT=8000

# Symbols to monitor
SYMBOLS=["NIFTY", "BANKNIFTY", "FINNIFTY"]
```

### Detection Algorithm Configuration

Each detector can be configured independently:

```python
# Spoofing detector config
spoofing_config = {
    "qty_threshold": 1000,
    "time_window_seconds": 30,
    "min_price_impact": 0.01,
    "confidence_base": 0.6
}

# Gamma squeeze detector config
gamma_config = {
    "gamma_exposure_threshold": 1000000.0,
    "volume_ratio_threshold": 0.5,
    "min_oi_threshold": 1000
}
```

## API Usage

### Get Recent Signals
```bash
curl "http://localhost:8080/signals?hours=24&min_confidence=0.8"
```

### Get Signal Summary
```bash
curl "http://localhost:8080/signals/summary?hours=24"
```

### Get System Statistics
```bash
curl "http://localhost:8080/statistics"
```

### Trigger Detection Cycle
```bash
curl -X POST "http://localhost:8080/detection/run"
```

### Health Check
```bash
curl "http://localhost:8080/health"
```

## Monitoring

### Prometheus Metrics
- Data collection metrics (requests, latency, errors)
- Detection algorithm performance
- System resource usage
- Business metrics (signals generated, estimated profits)

Access Prometheus at: `http://localhost:9090`

### Grafana Dashboards
- Real-time system overview
- Detection algorithm performance
- Market manipulation trends
- System health monitoring

Access Grafana at: `http://localhost:3000` (admin/admin123)

### Logs
Structured logging with configurable levels:
```bash
# View logs
docker-compose logs -f options_detector

# Log levels: DEBUG, INFO, WARNING, ERROR
```

## Data Sources

### NSE APIs
- **Options Chain**: `https://www.nseindia.com/api/option-chain-indices`
- **Market Status**: `https://www.nseindia.com/api/marketStatus`
- **Historical Data**: `https://www.nseindia.com/api/historical/cm/equity`

### Rate Limiting
- 60 requests per minute
- 2 requests per second
- Automatic retry with exponential backoff

## Detection Algorithms

### 1. Order Spoofing Detection
Identifies patterns where large orders are placed and quickly cancelled:
- Monitors bid/ask quantity changes
- Detects rapid order placement and removal
- Measures price impact
- Confidence scoring based on quantity size and timing

### 2. Gamma Squeeze Detection
Detects setups for gamma squeezes:
- Calculates gamma exposure by strike
- Monitors open interest concentration
- Tracks volume patterns
- Identifies strikes near underlying price

### 3. Volatility Skew Manipulation
Identifies artificial volatility skew creation:
- Monitors implied volatility changes
- Detects unnatural IV spikes
- Correlates with volume patterns
- Identifies coordinated strikes

## Development

### Project Structure
```
├── api/                    # FastAPI application
├── config/                 # Configuration management
├── core/                   # Core detection engine
├── data_collectors/        # Data collection modules
├── detection/              # Detection algorithms
├── models/                 # Data models and schemas
├── utils/                  # Utility modules
├── monitoring/             # Monitoring configuration
├── scripts/                # Deployment scripts
├── tests/                  # Test suite
├── docker-compose.yml      # Docker composition
├── Dockerfile             # Container definition
├── requirements.txt       # Python dependencies
└── main.py                # Application entry point
```

### Adding New Detectors

1. **Create detector class**
```python
from detection.base_detector import BaseDetector

class MyDetector(BaseDetector):
    def __init__(self, config=None):
        super().__init__("my_detector", config)
    
    async def detect(self, data):
        # Implementation
        return signals
    
    def get_required_data_window(self):
        return 20
```

2. **Register detector**
```python
from detection.base_detector import detector_registry

detector = MyDetector()
detector_registry.register(detector, priority=5)
```

### Testing
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test
pytest tests/test_spoofing_detector.py
```

## Deployment

### Docker Deployment
```bash
# Build and run
docker-compose up -d

# Scale detection workers
docker-compose up -d --scale options_detector=3

# View logs
docker-compose logs -f
```

### Kubernetes Deployment
```bash
# Apply configurations
kubectl apply -f k8s/

# Check status
kubectl get pods -n options-detection
```

### Production Considerations
- Use environment-specific configurations
- Set up proper logging and monitoring
- Configure SSL/TLS for API endpoints
- Implement proper authentication and authorization
- Set up automated backups for database
- Configure alerting for high-confidence signals

## Security

### API Security
- Rate limiting on API endpoints
- Input validation and sanitization
- CORS configuration
- Optional JWT authentication

### Data Security
- Database connection encryption
- Secure credential management
- Network isolation with Docker networks
- Regular security updates

## Performance

### Optimization Tips
- Use Redis caching for frequently accessed data
- Implement database connection pooling
- Configure appropriate batch sizes for data processing
- Monitor and tune detection algorithm parameters
- Use async processing for I/O operations

### Scaling
- Horizontal scaling with multiple detection workers
- Database read replicas for analytics
- Redis clustering for high availability
- Load balancing for API endpoints

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
```bash
# Check database status
docker-compose ps postgres
docker-compose logs postgres
```

2. **Redis Connection Errors**
```bash
# Check Redis status
docker-compose ps redis
docker-compose logs redis
```

3. **NSE API Rate Limiting**
```bash
# Check API statistics
curl "http://localhost:8080/statistics"
```

4. **High Memory Usage**
```bash
# Monitor system metrics
curl "http://localhost:8080/statistics"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This system is for educational and research purposes only. Always comply with applicable laws and regulations when monitoring financial markets. The authors are not responsible for any misuse of this system.
