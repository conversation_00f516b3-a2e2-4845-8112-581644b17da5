version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: options_postgres
    environment:
      POSTGRES_DB: options_db
      POSTGRES_USER: options_user
      POSTGRES_PASSWORD: options_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U options_user -d options_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - options_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: options_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - options_network

  # Options Detection System
  options_detector:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: options_detector
    environment:
      # Database settings
      DB_URL: ********************************************************/options_db
      
      # Redis settings
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0
      
      # API settings
      API_HOST: 0.0.0.0
      API_PORT: 8080
      
      # Monitoring settings
      MONITORING_PROMETHEUS_PORT: 8000
      MONITORING_LOG_LEVEL: INFO
      
      # Detection settings
      DETECTION_INTERVAL: 60
      
      # Environment
      ENVIRONMENT: production
      DEBUG: false
    ports:
      - "8080:8080"  # API port
      - "8000:8000"  # Prometheus metrics port
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - options_network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: options_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    depends_on:
      - options_detector
    networks:
      - options_network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: options_grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - options_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: options_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - options_detector
      - grafana
    networks:
      - options_network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  options_network:
    driver: bridge
