"""
Regime Kill-Switch System for automatic system shutdown during adverse conditions
Implements quantitative kill-switches to prevent catastrophic losses
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import statistics

logger = logging.getLogger(__name__)

class KillSwitchTrigger(str, Enum):
    """Types of kill switch triggers"""
    DRAWDOWN_LIMIT = "drawdown_limit"
    CONSECUTIVE_LOSSES = "consecutive_losses"
    VOLATILITY_SPIKE = "volatility_spike"
    LATENCY_DEGRADATION = "latency_degradation"
    DATA_QUALITY_FAILURE = "data_quality_failure"
    REGIME_EXTREME = "regime_extreme"
    MANUAL_OVERRIDE = "manual_override"

class KillSwitchSeverity(str, Enum):
    """Severity levels for kill switch activation"""
    WARNING = "warning"      # Log warning, continue operation
    PAUSE = "pause"          # Pause new trades, keep monitoring
    SHUTDOWN = "shutdown"    # Full system shutdown
    EMERGENCY = "emergency"  # Immediate emergency shutdown

@dataclass
class KillSwitchRule:
    """Definition of a kill switch rule"""
    name: str
    trigger_type: KillSwitchTrigger
    severity: KillSwitchSeverity
    threshold: float
    lookback_periods: int
    enabled: bool = True
    description: str = ""
    
    # Callback functions
    warning_callback: Optional[Callable] = None
    trigger_callback: Optional[Callable] = None

@dataclass
class KillSwitchEvent:
    """Record of a kill switch activation"""
    timestamp: datetime
    rule_name: str
    trigger_type: KillSwitchTrigger
    severity: KillSwitchSeverity
    trigger_value: float
    threshold: float
    description: str
    system_state: Dict[str, Any] = field(default_factory=dict)

class RegimeKillSwitch:
    """
    Comprehensive kill-switch system that monitors multiple risk factors
    and automatically shuts down the system when thresholds are breached
    """
    
    def __init__(self):
        self.rules: Dict[str, KillSwitchRule] = {}
        self.events: List[KillSwitchEvent] = []
        self.system_active = True
        self.system_paused = False
        
        # Performance tracking
        self.trade_history: List[Dict[str, Any]] = []
        self.latency_history: List[float] = []
        self.data_quality_history: List[float] = []
        
        # Callbacks
        self.shutdown_callbacks: List[Callable] = []
        self.pause_callbacks: List[Callable] = []
        self.warning_callbacks: List[Callable] = []
        
        # Initialize default rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default kill switch rules"""
        
        # 1. Drawdown Kill Switch
        self.add_rule(KillSwitchRule(
            name="max_drawdown",
            trigger_type=KillSwitchTrigger.DRAWDOWN_LIMIT,
            severity=KillSwitchSeverity.SHUTDOWN,
            threshold=0.25,  # 25% drawdown
            lookback_periods=1,
            description="Maximum portfolio drawdown limit"
        ))
        
        # 2. Consecutive Losses Kill Switch
        self.add_rule(KillSwitchRule(
            name="consecutive_losses",
            trigger_type=KillSwitchTrigger.CONSECUTIVE_LOSSES,
            severity=KillSwitchSeverity.PAUSE,
            threshold=5,  # 5 consecutive losses
            lookback_periods=10,
            description="Maximum consecutive losing trades"
        ))
        
        # 3. Volatility Spike Kill Switch
        self.add_rule(KillSwitchRule(
            name="volatility_spike",
            trigger_type=KillSwitchTrigger.VOLATILITY_SPIKE,
            severity=KillSwitchSeverity.PAUSE,
            threshold=0.6,  # 60% implied volatility
            lookback_periods=1,
            description="Extreme volatility spike protection"
        ))
        
        # 4. Latency Degradation Kill Switch
        self.add_rule(KillSwitchRule(
            name="latency_degradation",
            trigger_type=KillSwitchTrigger.LATENCY_DEGRADATION,
            severity=KillSwitchSeverity.WARNING,
            threshold=500.0,  # 500ms average latency
            lookback_periods=10,
            description="System latency degradation warning"
        ))
        
        # 5. Data Quality Kill Switch
        self.add_rule(KillSwitchRule(
            name="data_quality_failure",
            trigger_type=KillSwitchTrigger.DATA_QUALITY_FAILURE,
            severity=KillSwitchSeverity.PAUSE,
            threshold=0.5,  # 50% data quality score
            lookback_periods=5,
            description="Poor data quality protection"
        ))
    
    def add_rule(self, rule: KillSwitchRule):
        """Add a new kill switch rule"""
        self.rules[rule.name] = rule
        logger.info(f"Added kill switch rule: {rule.name} ({rule.severity})")
    
    def remove_rule(self, rule_name: str):
        """Remove a kill switch rule"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed kill switch rule: {rule_name}")
    
    def enable_rule(self, rule_name: str):
        """Enable a specific rule"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
            logger.info(f"Enabled kill switch rule: {rule_name}")
    
    def disable_rule(self, rule_name: str):
        """Disable a specific rule"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
            logger.info(f"Disabled kill switch rule: {rule_name}")
    
    def update_trade_performance(self, trade_pnl: float, is_winning_trade: bool):
        """Update trade performance metrics"""
        self.trade_history.append({
            'timestamp': datetime.now(),
            'pnl': trade_pnl,
            'is_winning': is_winning_trade
        })
        
        # Keep only recent history
        if len(self.trade_history) > 100:
            self.trade_history = self.trade_history[-100:]
        
        # Check drawdown and consecutive losses
        self._check_performance_rules()
    
    def update_latency_metrics(self, latency_ms: float):
        """Update system latency metrics"""
        self.latency_history.append(latency_ms)
        
        # Keep only recent history
        if len(self.latency_history) > 50:
            self.latency_history = self.latency_history[-50:]
        
        # Check latency rules
        self._check_latency_rules()
    
    def update_data_quality(self, quality_score: float):
        """Update data quality metrics (0.0 to 1.0)"""
        self.data_quality_history.append(quality_score)
        
        # Keep only recent history
        if len(self.data_quality_history) > 20:
            self.data_quality_history = self.data_quality_history[-20:]
        
        # Check data quality rules
        self._check_data_quality_rules()
    
    def update_volatility_metrics(self, implied_volatility: float):
        """Update volatility metrics and check rules"""
        self._check_volatility_rules(implied_volatility)
    
    def _check_performance_rules(self):
        """Check performance-based kill switch rules"""
        if not self.trade_history:
            return
        
        # Check drawdown
        if "max_drawdown" in self.rules and self.rules["max_drawdown"].enabled:
            current_drawdown = self._calculate_current_drawdown()
            if current_drawdown >= self.rules["max_drawdown"].threshold:
                self._trigger_kill_switch("max_drawdown", current_drawdown)
        
        # Check consecutive losses
        if "consecutive_losses" in self.rules and self.rules["consecutive_losses"].enabled:
            consecutive_losses = self._calculate_consecutive_losses()
            if consecutive_losses >= self.rules["consecutive_losses"].threshold:
                self._trigger_kill_switch("consecutive_losses", consecutive_losses)
    
    def _check_latency_rules(self):
        """Check latency-based kill switch rules"""
        if not self.latency_history or "latency_degradation" not in self.rules:
            return
        
        rule = self.rules["latency_degradation"]
        if not rule.enabled:
            return
        
        # Calculate average latency over lookback period
        recent_latencies = self.latency_history[-rule.lookback_periods:]
        avg_latency = statistics.mean(recent_latencies)
        
        if avg_latency >= rule.threshold:
            self._trigger_kill_switch("latency_degradation", avg_latency)
    
    def _check_data_quality_rules(self):
        """Check data quality kill switch rules"""
        if not self.data_quality_history or "data_quality_failure" not in self.rules:
            return
        
        rule = self.rules["data_quality_failure"]
        if not rule.enabled:
            return
        
        # Calculate average data quality over lookback period
        recent_quality = self.data_quality_history[-rule.lookback_periods:]
        avg_quality = statistics.mean(recent_quality)
        
        if avg_quality <= rule.threshold:
            self._trigger_kill_switch("data_quality_failure", avg_quality)
    
    def _check_volatility_rules(self, implied_volatility: float):
        """Check volatility-based kill switch rules"""
        if "volatility_spike" not in self.rules:
            return
        
        rule = self.rules["volatility_spike"]
        if not rule.enabled:
            return
        
        if implied_volatility >= rule.threshold:
            self._trigger_kill_switch("volatility_spike", implied_volatility)
    
    def _calculate_current_drawdown(self) -> float:
        """Calculate current portfolio drawdown"""
        if not self.trade_history:
            return 0.0
        
        # Calculate cumulative PnL
        cumulative_pnl = 0.0
        peak_pnl = 0.0
        max_drawdown = 0.0
        
        for trade in self.trade_history:
            cumulative_pnl += trade['pnl']
            peak_pnl = max(peak_pnl, cumulative_pnl)
            
            if peak_pnl > 0:
                current_drawdown = (peak_pnl - cumulative_pnl) / peak_pnl
                max_drawdown = max(max_drawdown, current_drawdown)
        
        return max_drawdown
    
    def _calculate_consecutive_losses(self) -> int:
        """Calculate current consecutive losing trades"""
        if not self.trade_history:
            return 0
        
        consecutive_losses = 0
        
        # Count from most recent trade backwards
        for trade in reversed(self.trade_history):
            if not trade['is_winning']:
                consecutive_losses += 1
            else:
                break
        
        return consecutive_losses
    
    def _trigger_kill_switch(self, rule_name: str, trigger_value: float):
        """Trigger a kill switch rule"""
        rule = self.rules[rule_name]
        
        # Create event record
        event = KillSwitchEvent(
            timestamp=datetime.now(),
            rule_name=rule_name,
            trigger_type=rule.trigger_type,
            severity=rule.severity,
            trigger_value=trigger_value,
            threshold=rule.threshold,
            description=rule.description,
            system_state=self._get_system_state()
        )
        
        self.events.append(event)
        
        # Log the event
        logger.critical(
            f"KILL SWITCH TRIGGERED: {rule_name} - {rule.description} "
            f"(value: {trigger_value:.3f}, threshold: {rule.threshold:.3f})"
        )
        
        # Execute appropriate action
        if rule.severity == KillSwitchSeverity.WARNING:
            self._handle_warning(event)
        elif rule.severity == KillSwitchSeverity.PAUSE:
            self._handle_pause(event)
        elif rule.severity in [KillSwitchSeverity.SHUTDOWN, KillSwitchSeverity.EMERGENCY]:
            self._handle_shutdown(event)
        
        # Call rule-specific callback
        if rule.trigger_callback:
            try:
                rule.trigger_callback(event)
            except Exception as e:
                logger.error(f"Error in kill switch callback: {e}")
    
    def _handle_warning(self, event: KillSwitchEvent):
        """Handle warning-level kill switch"""
        for callback in self.warning_callbacks:
            try:
                callback(event)
            except Exception as e:
                logger.error(f"Error in warning callback: {e}")
    
    def _handle_pause(self, event: KillSwitchEvent):
        """Handle pause-level kill switch"""
        self.system_paused = True
        logger.warning("SYSTEM PAUSED due to kill switch activation")
        
        for callback in self.pause_callbacks:
            try:
                callback(event)
            except Exception as e:
                logger.error(f"Error in pause callback: {e}")
    
    def _handle_shutdown(self, event: KillSwitchEvent):
        """Handle shutdown-level kill switch"""
        self.system_active = False
        self.system_paused = True
        logger.critical("SYSTEM SHUTDOWN due to kill switch activation")
        
        for callback in self.shutdown_callbacks:
            try:
                callback(event)
            except Exception as e:
                logger.error(f"Error in shutdown callback: {e}")
    
    def _get_system_state(self) -> Dict[str, Any]:
        """Get current system state for event logging"""
        return {
            'system_active': self.system_active,
            'system_paused': self.system_paused,
            'total_trades': len(self.trade_history),
            'recent_drawdown': self._calculate_current_drawdown(),
            'consecutive_losses': self._calculate_consecutive_losses(),
            'avg_latency': statistics.mean(self.latency_history) if self.latency_history else 0,
            'avg_data_quality': statistics.mean(self.data_quality_history) if self.data_quality_history else 1.0
        }
    
    def manual_shutdown(self, reason: str = "Manual override"):
        """Manually trigger system shutdown"""
        event = KillSwitchEvent(
            timestamp=datetime.now(),
            rule_name="manual_override",
            trigger_type=KillSwitchTrigger.MANUAL_OVERRIDE,
            severity=KillSwitchSeverity.SHUTDOWN,
            trigger_value=1.0,
            threshold=1.0,
            description=reason,
            system_state=self._get_system_state()
        )
        
        self.events.append(event)
        self._handle_shutdown(event)
    
    def reset_system(self):
        """Reset system to active state (use with caution)"""
        self.system_active = True
        self.system_paused = False
        logger.info("System reset to active state")
    
    def should_allow_trading(self) -> tuple[bool, str]:
        """Check if trading should be allowed"""
        if not self.system_active:
            return False, "System shutdown by kill switch"
        
        if self.system_paused:
            return False, "System paused by kill switch"
        
        return True, "System operational"
    
    def get_status_summary(self) -> Dict[str, Any]:
        """Get comprehensive status summary"""
        return {
            'system_active': self.system_active,
            'system_paused': self.system_paused,
            'total_events': len(self.events),
            'recent_events': [
                {
                    'timestamp': event.timestamp,
                    'rule_name': event.rule_name,
                    'severity': event.severity,
                    'description': event.description
                }
                for event in self.events[-5:]  # Last 5 events
            ],
            'active_rules': [
                {
                    'name': rule.name,
                    'type': rule.trigger_type,
                    'severity': rule.severity,
                    'threshold': rule.threshold,
                    'enabled': rule.enabled
                }
                for rule in self.rules.values() if rule.enabled
            ],
            'current_metrics': self._get_system_state()
        }
    
    def add_shutdown_callback(self, callback: Callable):
        """Add callback for system shutdown events"""
        self.shutdown_callbacks.append(callback)
    
    def add_pause_callback(self, callback: Callable):
        """Add callback for system pause events"""
        self.pause_callbacks.append(callback)
    
    def add_warning_callback(self, callback: Callable):
        """Add callback for warning events"""
        self.warning_callbacks.append(callback)

# Global regime kill switch instance
regime_kill_switch = RegimeKillSwitch()
