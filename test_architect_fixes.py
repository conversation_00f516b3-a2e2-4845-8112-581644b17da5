"""
Test The Architect's Fixes
Comprehensive test of all brutal reality improvements
"""
import asyncio
import numpy as np
from datetime import datetime, timedelta

# Import all the fixed components
from analysis.lag_analysis_framework import (
    LagAnalysisFramework, InstitutionalEvent, RetailResponse
)
from execution.realistic_execution_model import (
    RealisticExecutionModel, MarketConditions, OrderType
)
from risk_management.quantitative_regime_filters import (
    QuantitativeRegimeFilter, FIIFlowData, MarketRegime
)
from risk_management.mechanical_kill_switches import (
    MechanicalKillSwitches, TradeResult
)

async def test_architect_fixes():
    print("🏗️  Testing The Architect's Brutal Reality Fixes")
    print("=" * 80)

    # Test 1: Lag Analysis Framework
    print("\n1️⃣  Testing Lag Analysis Framework")
    print("-" * 50)

    lag_analyzer = LagAnalysisFramework()

    # Create sample institutional events
    institutional_events = []
    base_time = datetime.now() - timedelta(days=30)

    for i in range(60):  # Need more samples for statistical significance
        event = InstitutionalEvent(
            timestamp=base_time + timedelta(hours=i*6),
            symbol="NIFTY",
            flow_type="call_buying",
            notional_crores=75.0 + i*10,
            volume_sigma=3.5 + i*0.1,
            confidence=0.8 + i*0.01
        )
        institutional_events.append(event)

    # Create sample retail responses (with lag)
    retail_responses = []
    for i, inst_event in enumerate(institutional_events):
        # Retail responds 2-4 hours later
        lag_hours = 2 + (i % 3)  # 2-4 hour lag
        response = RetailResponse(
            timestamp=inst_event.timestamp + timedelta(hours=lag_hours),
            symbol="NIFTY",
            price_change_bps=50 + i*5,  # Positive response to call buying
            volume_change_ratio=1.5 + i*0.1,
            direction="bullish"
        )
        retail_responses.append(response)

    # Analyze lag
    lag_results = lag_analyzer.analyze_institutional_retail_lag(
        institutional_events, retail_responses
    )

    print(f"✅ Lag analysis completed")
    print(f"   Symbols analyzed: {len(lag_results)}")
    for symbol, result in lag_results.items():
        print(f"   {symbol}: {result.optimal_lag_minutes} min lag, "
              f"{result.correlation_coefficient:.3f} correlation, "
              f"p-value: {result.statistical_significance:.4f}")

    # Test 2: Realistic Execution Model
    print("\n2️⃣  Testing Realistic Execution Model")
    print("-" * 50)

    execution_model = RealisticExecutionModel()

    # Create adverse market conditions
    market_conditions = MarketConditions(
        bid_ask_spread_bps=300,  # 3% spread
        volume_last_hour=25000,  # Low volume
        volatility_percentile=85,  # High volatility
        time_to_expiry_hours=12,  # Near expiry
        open_interest=100000,
        is_near_expiry=True,
        is_high_volatility=True
    )

    # Execute multiple orders to see brutal reality
    execution_results = []
    for i in range(10):
        result = execution_model.execute_order(
            symbol="NIFTY",
            strike=25000,
            option_type="CE",
            quantity=10 + i*5,
            order_type=OrderType.MARKET,
            market_conditions=market_conditions,
            current_price=150.0
        )
        execution_results.append(result)

    # Get execution summary
    summary = execution_model.get_execution_summary(execution_results)

    print(f"✅ Execution model tested with {len(execution_results)} orders")
    print(f"   Fill rate: {summary.get('fill_rate', 0):.1%}")
    print(f"   Average slippage: {summary.get('avg_slippage_bps', 0):.0f} bps")
    print(f"   Average market impact: {summary.get('avg_market_impact_bps', 0):.0f} bps")
    print(f"   Rejections: {summary.get('rejections', 0)}")
    print(f"   Total fees: ₹{summary.get('total_fees', 0):,.0f}")

    # Test 3: Quantitative Regime Filters
    print("\n3️⃣  Testing Quantitative Regime Filters")
    print("-" * 50)

    regime_filter = QuantitativeRegimeFilter()

    # Simulate FII selling spree
    for i in range(5):
        fii_data = FIIFlowData(
            date=datetime.now() - timedelta(days=4-i),
            equity_flow_crores=-2500 - i*200,  # Heavy selling
            debt_flow_crores=100,
            total_flow_crores=-2400 - i*200,
            cumulative_flow_crores=-12000 - i*1000
        )
        regime_filter.update_fii_flow_data(fii_data)

    regime_status = regime_filter.get_current_regime_status()

    print(f"✅ Regime filter tested")
    print(f"   Current regime: {regime_status.regime.value}")
    print(f"   Should trade: {regime_status.should_trade}")
    print(f"   Risk level: {regime_status.risk_level:.1%}")
    print(f"   Reason: {regime_status.reason}")
    print(f"   Days in regime: {regime_status.days_in_regime}")

    # Test 4: Mechanical Kill Switches
    print("\n4️⃣  Testing Mechanical Kill Switches")
    print("-" * 50)

    kill_switches = MechanicalKillSwitches()

    # Simulate a series of losing trades
    base_time = datetime.now() - timedelta(days=10)

    for i in range(8):  # 8 consecutive losses to trigger kill switch
        trade = TradeResult(
            timestamp=base_time + timedelta(hours=i*6),
            symbol="NIFTY",
            entry_price=150.0,
            exit_price=140.0 - i*2,  # Getting worse
            quantity=10,
            pnl=-1000 - i*200,  # Increasing losses
            confidence=0.8 - i*0.05,  # Decreasing confidence
            was_winner=False
        )
        kill_switches.add_trade_result(trade)

    kill_status = kill_switches.get_kill_switch_status()

    print(f"✅ Kill switches tested")
    print(f"   Kill switch triggered: {kill_status.is_triggered}")
    print(f"   Trigger type: {kill_status.trigger_type}")
    print(f"   Reason: {kill_status.trigger_reason}")
    print(f"   Current drawdown: {kill_status.current_drawdown:.1f}%")
    print(f"   Consecutive losses: {kill_status.consecutive_losses}")
    print(f"   Trading allowed: {kill_switches.should_allow_trading()}")

    # Test 5: Integration Test
    print("\n5️⃣  Integration Test: All Systems Working Together")
    print("-" * 50)

    # Check if we should trade based on all filters
    should_trade_regime = regime_status.should_trade
    should_trade_kill_switch = kill_switches.should_allow_trading()

    overall_should_trade = should_trade_regime and should_trade_kill_switch

    print(f"✅ Integration test completed")
    print(f"   Regime allows trading: {should_trade_regime}")
    print(f"   Kill switches allow trading: {should_trade_kill_switch}")
    print(f"   OVERALL TRADING PERMISSION: {overall_should_trade}")

    if not overall_should_trade:
        print(f"   🚨 TRADING HALTED 🚨")
        if not should_trade_regime:
            print(f"      Reason: {regime_status.reason}")
        if not should_trade_kill_switch:
            print(f"      Reason: {kill_status.trigger_reason}")

    print("\n" + "=" * 80)
    print("🏗️  The Architect's Fixes: COMPLETE")
    print("   ✅ Lag analysis framework implemented")
    print("   ✅ Realistic execution model with brutal slippage")
    print("   ✅ Quantitative regime filters with kill switches")
    print("   ✅ Mechanical kill switches (no emotions)")
    print("   ✅ All systems integrated and tested")
    print("\n💀 No more fantasy trading. Welcome to brutal reality.")

if __name__ == "__main__":
    asyncio.run(test_architect_fixes())