"""
Realistic Backtesting Module for Options Manipulation Detection System

This module provides comprehensive backtesting capabilities that simulate
real NSE trading conditions including spreads, slippage, partial fills,
and rejection rates.
"""

from .realistic_backtester import (
    RealisticBacktester,
    BacktestTrade,
    ExecutionResult,
    MarketConditions,
    FillType,
    realistic_backtester
)

__all__ = [
    'RealisticBacktester',
    'BacktestTrade', 
    'ExecutionResult',
    'MarketConditions',
    'FillType',
    'realistic_backtester'
]
