"""
Test the enhanced Indian options manipulation detection system
"""
import asyncio
import sys
from datetime import datetime, timedelta

from core.detection_engine import detection_engine
from models.data_models import OptionsData, OptionType

async def test_enhanced_system():
    print('Testing Enhanced Indian Options Manipulation Detection System')
    print('=' * 70)

    try:
        # Initialize the detection engine
        await detection_engine.initialize()

        # Create sample Indian options data
        sample_data = []
        base_time = datetime.now()

        # NIFTY options data with institutional flow pattern
        for i in range(10):
            sample_data.append(OptionsData(
                symbol='NIFTY',
                strike=25000.0,  # Near ATM
                option_type=OptionType.CALL,
                expiry_date=base_time + timedelta(days=3),  # Short expiry
                last_price=150.0 + i,
                bid_price=149.0 + i,
                ask_price=151.0 + i,
                bid_qty=500 + i * 100,
                ask_qty=600 + i * 100,
                volume=5000 + i * 2000,  # Increasing volume
                open_interest=50000 + i * 10000,  # Increasing OI
                timestamp=base_time + timedelta(minutes=i)
            ))

        print(f'Created {len(sample_data)} sample data points')
        print(f'Volume range: {min(d.volume for d in sample_data):,} - {max(d.volume for d in sample_data):,}')
        print(f'OI range: {min(d.open_interest for d in sample_data):,} - {max(d.open_interest for d in sample_data):,}')

        # Run detection
        print('\nRunning detection algorithms...')
        signals = await detection_engine.run_detection_cycle()

        print(f'\nDetection Results:')
        print(f'Total signals detected: {len(signals)}')

        for i, signal in enumerate(signals, 1):
            print(f'\nSignal {i}:')
            print(f'  Pattern: {signal.pattern_type.value}')
            print(f'  Confidence: {signal.confidence:.1%}')
            print(f'  Description: {signal.description}')
            print(f'  Estimated Profit: Rs{signal.estimated_profit:,.0f}')
            print(f'  Symbols: {signal.symbols_affected}')

        # Get system statistics
        stats = detection_engine.get_statistics()
        print(f'\nSystem Statistics:')
        print(f'  Detectors active: {stats["detector_count"]}')
        print(f'  Total signals: {stats["total_signals"]}')
        print(f'  High confidence signals: {stats["high_confidence_signals"]}')

        print('\n✅ Enhanced system test completed successfully!')

    except Exception as e:
        print(f'❌ Test failed: {str(e)}')
        import traceback
        traceback.print_exc()

    finally:
        await detection_engine.shutdown()

if __name__ == "__main__":
    asyncio.run(test_enhanced_system())