"""
FastAPI application for the Options Manipulation Detection System
"""
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
from contextlib import asynccontextmanager
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from core.detection_engine import detection_engine
from models.data_models import APIResponse, ManipulationSignal, SystemMetrics
from utils.database import db_manager
from utils.cache import cache_manager
from utils.metrics import metrics_collector
from config.settings import settings
from paper_trading.api import paper_trading_router
from utils.latency_tracker import latency_tracker
from utils.adaptive_thresholds import adaptive_threshold_manager
from utils.volatility_regime_filter import volatility_regime_filter
from utils.regime_kill_switch import regime_kill_switch

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.monitoring.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Options Manipulation Detection API")
    try:
        await detection_engine.initialize()
        logger.info("Detection engine initialized")
    except Exception as e:
        logger.error(f"Failed to initialize detection engine: {str(e)}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Options Manipulation Detection API")
    await detection_engine.shutdown()

# Create FastAPI app
app = FastAPI(
    title="Options Manipulation Detection System",
    description="Real-time detection of manipulation patterns in Indian options markets",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include paper trading router
app.include_router(paper_trading_router)

@app.get("/", response_model=APIResponse)
async def root():
    """Root endpoint"""
    return APIResponse(
        success=True,
        message="Options Manipulation Detection System API",
        data={
            "version": "1.0.0",
            "status": "running",
            "timestamp": datetime.now()
        }
    )

@app.get("/health", response_model=APIResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check system components
        health_status = {
            "api": "healthy",
            "database": "unknown",
            "cache": "unknown",
            "detection_engine": "unknown"
        }
        
        # Check database
        try:
            async with db_manager.get_session() as session:
                await session.execute("SELECT 1")
            health_status["database"] = "healthy"
        except Exception as e:
            health_status["database"] = f"unhealthy: {str(e)}"
        
        # Check cache
        try:
            await cache_manager.redis_client.ping()
            health_status["cache"] = "healthy"
        except Exception as e:
            health_status["cache"] = f"unhealthy: {str(e)}"
        
        # Check detection engine
        if detection_engine.running:
            health_status["detection_engine"] = "running"
        else:
            health_status["detection_engine"] = "stopped"
        
        overall_healthy = all(
            status == "healthy" or status == "running" 
            for status in health_status.values()
        )
        
        return APIResponse(
            success=overall_healthy,
            message="Health check completed",
            data=health_status
        )

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return APIResponse(
            success=False,
            message=f"Health check failed: {str(e)}",
            data={"error": str(e)}
        )

@app.get("/system/status", response_model=APIResponse)
async def system_status():
    """Comprehensive system status including all new monitoring systems"""
    try:
        # Get latency statistics
        latency_stats = latency_tracker.get_all_stats()
        latency_health = latency_tracker.is_system_healthy()

        # Get adaptive threshold status
        threshold_stats = adaptive_threshold_manager.get_all_thresholds()
        regime_info = adaptive_threshold_manager.get_regime_info()

        # Get volatility regime status
        volatility_regime = volatility_regime_filter.get_regime_summary()

        # Get kill switch status
        kill_switch_status = regime_kill_switch.get_status_summary()

        # Get detection engine stats
        detection_stats = detection_engine.get_statistics()

        status_data = {
            "timestamp": datetime.now(),
            "overall_health": {
                "system_active": kill_switch_status['system_active'],
                "system_paused": kill_switch_status['system_paused'],
                "latency_healthy": latency_health,
                "can_trade": regime_info.get('should_trade', False)
            },
            "latency_monitoring": {
                "healthy": latency_health,
                "stats": {name: {
                    "mean_ms": stats.mean_ms,
                    "p95_ms": stats.p95_ms,
                    "critical_rate": stats.critical_rate
                } for name, stats in latency_stats.items()},
                "recent_critical_events": len(latency_tracker.get_recent_critical_events(minutes=5))
            },
            "adaptive_thresholds": {
                "current_thresholds": threshold_stats,
                "market_regime": regime_info.get('current_regime'),
                "should_trade": regime_info.get('should_trade', False)
            },
            "volatility_regime": volatility_regime,
            "kill_switch": {
                "active": kill_switch_status['system_active'],
                "paused": kill_switch_status['system_paused'],
                "total_events": kill_switch_status['total_events'],
                "recent_events": kill_switch_status['recent_events'][-3:],  # Last 3 events
                "active_rules": len(kill_switch_status['active_rules'])
            },
            "detection_engine": {
                "running": detection_stats.get('running', False),
                "cycles_completed": detection_stats.get('cycles_completed', 0),
                "total_signals": detection_stats.get('total_signals', 0),
                "high_confidence_signals": detection_stats.get('high_confidence_signals', 0),
                "last_detection_time": detection_stats.get('last_detection_time')
            }
        }

        return APIResponse(
            success=True,
            message="System status retrieved successfully",
            data=status_data
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return APIResponse(
            success=False,
            message="Health check failed",
            errors=[str(e)]
        )

@app.get("/signals", response_model=APIResponse)
async def get_signals(
    hours: int = 24,
    min_confidence: float = 0.0,
    pattern_type: Optional[str] = None,
    limit: int = 100
):
    """Get recent manipulation signals"""
    try:
        signals = await db_manager.get_manipulation_signals(
            hours=hours,
            min_confidence=min_confidence
        )
        
        # Filter by pattern type if specified
        if pattern_type:
            signals = [s for s in signals if s.pattern_type.value == pattern_type]
        
        # Limit results
        signals = signals[:limit]
        
        # Convert to dict for JSON response
        signals_data = []
        for signal in signals:
            signals_data.append({
                "id": signal.id,
                "pattern_type": signal.pattern_type.value,
                "timestamp": signal.timestamp.isoformat(),
                "symbols_affected": signal.symbols_affected,
                "confidence": signal.confidence,
                "confidence_level": signal.confidence_level.value,
                "description": signal.description,
                "estimated_profit": signal.estimated_profit,
                "market_impact": signal.market_impact,
                "detection_algorithm": signal.detection_algorithm
            })
        
        return APIResponse(
            success=True,
            message=f"Retrieved {len(signals_data)} signals",
            data={
                "signals": signals_data,
                "total_count": len(signals_data),
                "filters": {
                    "hours": hours,
                    "min_confidence": min_confidence,
                    "pattern_type": pattern_type
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error retrieving signals: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/signals/summary", response_model=APIResponse)
async def get_signals_summary(hours: int = 24):
    """Get summary of recent signals"""
    try:
        signals = await db_manager.get_manipulation_signals(hours=hours)
        
        # Calculate summary statistics
        total_signals = len(signals)
        high_confidence_signals = len([s for s in signals if s.confidence >= 0.8])
        total_estimated_profit = sum(s.estimated_profit for s in signals)
        
        # Group by pattern type
        pattern_counts = {}
        for signal in signals:
            pattern_type = signal.pattern_type.value
            pattern_counts[pattern_type] = pattern_counts.get(pattern_type, 0) + 1
        
        # Group by confidence level
        confidence_counts = {}
        for signal in signals:
            confidence_level = signal.confidence_level.value
            confidence_counts[confidence_level] = confidence_counts.get(confidence_level, 0) + 1
        
        return APIResponse(
            success=True,
            message="Signal summary retrieved",
            data={
                "total_signals": total_signals,
                "high_confidence_signals": high_confidence_signals,
                "total_estimated_profit": total_estimated_profit,
                "pattern_breakdown": pattern_counts,
                "confidence_breakdown": confidence_counts,
                "time_period_hours": hours
            }
        )
        
    except Exception as e:
        logger.error(f"Error retrieving signal summary: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/statistics", response_model=APIResponse)
async def get_statistics():
    """Get system statistics"""
    try:
        # Get detection engine stats
        engine_stats = detection_engine.get_statistics()
        
        # Get metrics summary
        metrics_summary = metrics_collector.get_summary_stats()
        
        return APIResponse(
            success=True,
            message="Statistics retrieved",
            data={
                "detection_engine": engine_stats,
                "system_metrics": metrics_summary,
                "timestamp": datetime.now()
            }
        )
        
    except Exception as e:
        logger.error(f"Error retrieving statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/detection/run", response_model=APIResponse)
async def run_detection_cycle(background_tasks: BackgroundTasks):
    """Manually trigger a detection cycle"""
    try:
        # Run detection in background
        background_tasks.add_task(detection_engine.run_detection_cycle)
        
        return APIResponse(
            success=True,
            message="Detection cycle started",
            data={"status": "running"}
        )
        
    except Exception as e:
        logger.error(f"Error starting detection cycle: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/detectors", response_model=APIResponse)
async def get_detectors():
    """Get list of available detectors"""
    try:
        from detection.base_detector import detector_registry
        
        detectors_info = []
        for detector_name in detector_registry.list_detectors():
            detector = detector_registry.get_detector(detector_name)
            if detector:
                stats = detector.get_statistics()
                detectors_info.append({
                    "name": detector.name,
                    "enabled": detector.enabled,
                    "execution_count": stats["execution_count"],
                    "signals_generated": stats["signals_generated"],
                    "average_execution_time": stats["average_execution_time"],
                    "last_execution_time": stats["last_execution_time"].isoformat() if stats["last_execution_time"] else None
                })
        
        return APIResponse(
            success=True,
            message="Detectors information retrieved",
            data={"detectors": detectors_info}
        )
        
    except Exception as e:
        logger.error(f"Error retrieving detectors info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/detectors/{detector_name}/enable", response_model=APIResponse)
async def enable_detector(detector_name: str):
    """Enable a specific detector"""
    try:
        from detection.base_detector import detector_registry
        
        detector_registry.enable_detector(detector_name)
        
        return APIResponse(
            success=True,
            message=f"Detector {detector_name} enabled"
        )
        
    except Exception as e:
        logger.error(f"Error enabling detector {detector_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/detectors/{detector_name}/disable", response_model=APIResponse)
async def disable_detector(detector_name: str):
    """Disable a specific detector"""
    try:
        from detection.base_detector import detector_registry
        
        detector_registry.disable_detector(detector_name)
        
        return APIResponse(
            success=True,
            message=f"Detector {detector_name} disabled"
        )
        
    except Exception as e:
        logger.error(f"Error disabling detector {detector_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/metrics/prometheus")
async def get_prometheus_metrics():
    """Get Prometheus metrics (for scraping)"""
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    
    return Response(
        generate_latest(),
        media_type=CONTENT_TYPE_LATEST
    )

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        workers=settings.api.workers,
        reload=settings.api.reload
    )
