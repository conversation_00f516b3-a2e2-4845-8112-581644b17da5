# US Options Manipulation Detection Edge Definition

## Executive Summary

This document defines the quantifiable edge for detecting options manipulation in US markets, specifically targeting SPY and QQQ. The edge is based on exploiting the information asymmetry between institutional flow and retail sentiment through real-time options flow analysis.

## Core Manipulation Patterns to Detect

### 1. Gamma Squeeze Setup Detection
**Pattern:** Large call buying in short-dated, near-the-money options creating forced delta hedging

**Quantifiable Metrics:**
- **Volume Threshold:** >3 standard deviations above 20-day average volume
- **Gamma Exposure:** > net gamma exposure in single strike
- **Time to Expiration:** <7 days to expiration
- **Moneyness:** 0.95 < Strike/Spot < 1.05
- **Flow Direction:** >70% call buying vs put buying
- **Institutional Indicators:** Block trades >1000 contracts

**Entry Trigger:**
`
IF (volume > 3σ_20day) AND 
   (gamma_exposure > ) AND 
   (DTE < 7) AND 
   (0.95 < moneyness < 1.05) AND 
   (call_flow_ratio > 0.70) AND 
   (block_trades > 1000)
THEN signal_gamma_squeeze_setup()
`

### 2. Dark Pool Flow Divergence
**Pattern:** Options flow contradicting dark pool equity flow indicating informed positioning

**Quantifiable Metrics:**
- **Dark Pool Imbalance:** >60% buy/sell imbalance in dark pools
- **Options Flow Contradiction:** Opposite direction options flow > notional
- **Volume Confirmation:** Options volume >2σ above average
- **Time Window:** Divergence sustained for >15 minutes
- **Spread Tightening:** Bid-ask spread compression >20%

**Entry Trigger:**
`
IF (dark_pool_imbalance > 0.60) AND 
   (options_flow_direction != equity_flow_direction) AND 
   (options_notional > ) AND 
   (divergence_duration > 15_minutes) AND 
   (spread_compression > 0.20)
THEN signal_dark_pool_divergence()
`

### 3. Unusual Options Activity (UOA)
**Pattern:** Abnormal options activity preceding equity moves

**Quantifiable Metrics:**
- **Volume Spike:** >5σ above 30-day rolling average
- **Open Interest Change:** >50% increase in single session
- **Implied Volatility Skew:** >2σ deviation from historical skew
- **Put/Call Ratio:** >3σ deviation from 10-day average
- **Time Clustering:** >3 unusual strikes within 30-minute window

**Entry Trigger:**
`
IF (volume > 5σ_30day) AND 
   (OI_change > 0.50) AND 
   (IV_skew > 2σ_historical) AND 
   (put_call_ratio > 3σ_10day) AND 
   (clustered_strikes >= 3)
THEN signal_unusual_options_activity()
`

## Data Requirements

### Primary Data Sources
1. **CBOE Options Data**
   - Real-time options chains (all strikes, all expirations)
   - Level 2 bid/ask with size
   - Trade-by-trade data with timestamps
   - Open interest changes

2. **Dark Pool Flow Data**
   - ATS (Alternative Trading System) volume
   - Block trade notifications
   - Institutional flow indicators

3. **Market Microstructure Data**
   - Order book depth
   - Bid-ask spread dynamics
   - Market maker inventory levels

### Data Quality Requirements
- **Latency:** <50ms from exchange to analysis
- **Completeness:** >99.5% data capture rate
- **Accuracy:** Validated against exchange official data
- **Coverage:** All SPY/QQQ options chains

## Risk Management Framework

### Position Sizing Rules
- **Maximum Risk per Trade:** 0.5% of portfolio
- **Maximum Daily Risk:** 2.0% of portfolio
- **Maximum Concentration:** 20% in single underlying
- **Liquidity Requirement:** > average daily volume

### Entry Validation Checklist
1. **Market Conditions Check**
   - VIX < 40 (avoid extreme volatility)
   - Market hours only (9:30 AM - 4:00 PM ET)
   - No major economic events within 2 hours

2. **Technical Validation**
   - Signal confidence >75%
   - Multiple pattern confirmation
   - Risk/reward ratio >2:1

3. **Execution Feasibility**
   - Bid-ask spread <10% of mid price
   - Available liquidity >2x position size
   - No circuit breaker conditions

### Exit Rules
- **Profit Target:** 50% of maximum theoretical profit
- **Stop Loss:** 2x Average True Range from entry
- **Time Stop:** Close position 1 hour before expiration
- **Volatility Stop:** Exit if VIX spikes >20% intraday

## Backtesting Validation Framework

### Historical Events to Test
1. **GameStop Squeeze (Jan 2021)**
   - Expected: Gamma squeeze detection 2-3 days prior
   - Validation: Options volume spike >10σ

2. **Tesla Battery Day (Sep 2020)**
   - Expected: UOA detection in weekly calls
   - Validation: Dark pool flow divergence

3. **SPAC Mania (Feb 2021)**
   - Expected: Multiple UOA signals
   - Validation: IV skew anomalies

### Performance Metrics
- **Detection Rate:** >80% of known manipulation events
- **False Positive Rate:** <15% of total signals
- **Average Lead Time:** 2-6 hours before major moves
- **Risk-Adjusted Return:** Sharpe ratio >1.5

## Implementation Priorities

### Phase 1: Core Detection (Weeks 1-2)
1. SPY/QQQ options data pipeline
2. Gamma squeeze detection algorithm
3. Basic risk management framework

### Phase 2: Advanced Patterns (Weeks 3-4)
1. Dark pool flow integration
2. UOA detection refinement
3. Historical backtesting validation

### Phase 3: Production Deployment (Weeks 5-6)
1. Real-time execution system
2. Performance monitoring
3. Risk management automation

## Success Criteria

### Minimum Viable Product
- Detect 3 out of 5 known historical manipulation events
- Generate <20% false positives on clean data
- Execute paper trades with <100ms latency

### Production Ready
- 80%+ detection rate on validation set
- <15% false positive rate
- Sharpe ratio >1.5 in paper trading
- Full risk management automation

This edge definition provides the quantitative foundation for building a legitimate options manipulation detection system focused on exploiting information asymmetries rather than naive pattern matching.
