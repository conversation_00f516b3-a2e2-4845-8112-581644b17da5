"""
Quantitative Regime Filters
The Architect's Fix #3: Implement Quantitative Regime Filters

Mechanical kill switches for FII selling sprees, election periods, RBI policy weeks.
No hope-based risk management - only brutal quantitative reality.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime classifications"""
    NORMAL = "normal"
    FII_SELLING_SPREE = "fii_selling_spree"
    ELECTION_PERIOD = "election_period"
    RBI_POLICY_WEEK = "rbi_policy_week"
    HIGH_VOLATILITY = "high_volatility"
    LOW_LIQUIDITY = "low_liquidity"
    EXTREME_RISK = "extreme_risk"

@dataclass
class RegimeStatus:
    """Current regime status"""
    regime: MarketRegime
    should_trade: bool
    risk_level: float  # 0-1 scale
    reason: str
    days_in_regime: int
    next_review_date: datetime

@dataclass
class FIIFlowData:
    """FII flow data point"""
    date: datetime
    equity_flow_crores: float  # Positive = buying, Negative = selling
    debt_flow_crores: float
    total_flow_crores: float
    cumulative_flow_crores: float

class QuantitativeRegimeFilter:
    """
    Mechanical regime detection and trading kill switches
    No emotions, no hope - only quantitative rules
    """

    def __init__(self, config: Dict = None):
        self.config = config or {}

        # FII flow thresholds
        self.fii_selling_threshold_crores = self.config.get('fii_selling_threshold_crores', -2000)  # ₹2000 cr
        self.fii_selling_days_threshold = self.config.get('fii_selling_days_threshold', 3)

        # Election period settings
        self.election_blackout_days = self.config.get('election_blackout_days', 60)

        # RBI policy settings
        self.rbi_policy_blackout_days = self.config.get('rbi_policy_blackout_days', 3)  # 3 days around policy

        # Volatility thresholds
        self.high_volatility_vix_threshold = self.config.get('high_volatility_vix_threshold', 35)
        self.extreme_volatility_vix_threshold = self.config.get('extreme_volatility_vix_threshold', 50)

        # Liquidity thresholds
        self.low_liquidity_volume_threshold = self.config.get('low_liquidity_volume_threshold', 0.5)  # 50% of avg

        # Data storage
        self.fii_flow_history: List[FIIFlowData] = []
        self.current_regime = MarketRegime.NORMAL
        self.regime_start_date = datetime.now()

        # Known dates (should be updated regularly)
        self.election_dates = self._load_election_dates()
        self.rbi_policy_dates = self._load_rbi_policy_dates()

    def _load_election_dates(self) -> List[datetime]:
        """Load known election dates"""
        # Major election dates (should be updated)
        return [
            datetime(2024, 6, 4),   # Lok Sabha 2024 results
            datetime(2029, 5, 15),  # Estimated next Lok Sabha (update as needed)
        ]

    def _load_rbi_policy_dates(self) -> List[datetime]:
        """Load RBI policy meeting dates"""
        # RBI MPC meeting dates for 2024-2025 (update regularly)
        return [
            datetime(2024, 8, 8),   # August 2024
            datetime(2024, 10, 9),  # October 2024
            datetime(2024, 12, 6),  # December 2024
            datetime(2025, 2, 7),   # February 2025
            datetime(2025, 4, 10),  # April 2025
            datetime(2025, 6, 6),   # June 2025
        ]

    def update_fii_flow_data(self, flow_data: FIIFlowData):
        """
        Update FII flow data and recalculate regime

        Args:
            flow_data: Latest FII flow data
        """
        self.fii_flow_history.append(flow_data)

        # Keep only last 30 days
        cutoff_date = datetime.now() - timedelta(days=30)
        self.fii_flow_history = [
            f for f in self.fii_flow_history
            if f.date >= cutoff_date
        ]

        # Recalculate regime
        self._update_regime()

    def get_current_regime_status(self) -> RegimeStatus:
        """
        Get current market regime and trading permission

        Returns:
            Current regime status with trading decision
        """
        self._update_regime()

        should_trade = self._should_allow_trading()
        risk_level = self._calculate_risk_level()
        reason = self._get_regime_reason()
        days_in_regime = (datetime.now() - self.regime_start_date).days
        next_review = self._get_next_review_date()

        return RegimeStatus(
            regime=self.current_regime,
            should_trade=should_trade,
            risk_level=risk_level,
            reason=reason,
            days_in_regime=days_in_regime,
            next_review_date=next_review
        )

    def _update_regime(self):
        """Update current market regime based on all factors"""
        old_regime = self.current_regime

        # Check each regime condition in order of priority
        if self._is_extreme_risk_regime():
            new_regime = MarketRegime.EXTREME_RISK
        elif self._is_fii_selling_spree():
            new_regime = MarketRegime.FII_SELLING_SPREE
        elif self._is_election_period():
            new_regime = MarketRegime.ELECTION_PERIOD
        elif self._is_rbi_policy_week():
            new_regime = MarketRegime.RBI_POLICY_WEEK
        elif self._is_high_volatility_regime():
            new_regime = MarketRegime.HIGH_VOLATILITY
        elif self._is_low_liquidity_regime():
            new_regime = MarketRegime.LOW_LIQUIDITY
        else:
            new_regime = MarketRegime.NORMAL

        # Update regime if changed
        if new_regime != old_regime:
            logger.warning(f"REGIME CHANGE: {old_regime.value} -> {new_regime.value}")
            self.current_regime = new_regime
            self.regime_start_date = datetime.now()

    def _is_fii_selling_spree(self) -> bool:
        """
        Check if we're in a FII selling spree

        Returns:
            True if FII selling spree detected
        """
        if len(self.fii_flow_history) < self.fii_selling_days_threshold:
            return False

        # Check last N days for consecutive selling
        recent_flows = self.fii_flow_history[-self.fii_selling_days_threshold:]

        consecutive_selling_days = 0
        for flow in reversed(recent_flows):
            if flow.equity_flow_crores <= self.fii_selling_threshold_crores:
                consecutive_selling_days += 1
            else:
                break

        return consecutive_selling_days >= self.fii_selling_days_threshold

    def _is_election_period(self) -> bool:
        """
        Check if we're in election blackout period

        Returns:
            True if in election period
        """
        current_date = datetime.now()

        for election_date in self.election_dates:
            days_to_election = (election_date - current_date).days
            if 0 <= days_to_election <= self.election_blackout_days:
                return True

        return False

    def _is_rbi_policy_week(self) -> bool:
        """
        Check if we're in RBI policy week

        Returns:
            True if in RBI policy week
        """
        current_date = datetime.now()

        for policy_date in self.rbi_policy_dates:
            days_to_policy = abs((policy_date - current_date).days)
            if days_to_policy <= self.rbi_policy_blackout_days:
                return True

        return False

    def _is_high_volatility_regime(self) -> bool:
        """
        Check if we're in high volatility regime
        (This would need VIX data integration)

        Returns:
            True if high volatility detected
        """
        # Placeholder - would need actual VIX data
        # For now, assume normal volatility
        return False

    def _is_low_liquidity_regime(self) -> bool:
        """
        Check if we're in low liquidity regime
        (This would need volume data integration)

        Returns:
            True if low liquidity detected
        """
        # Placeholder - would need actual volume data
        # For now, assume normal liquidity
        return False

    def _is_extreme_risk_regime(self) -> bool:
        """
        Check for extreme risk conditions

        Returns:
            True if extreme risk detected
        """
        # Multiple adverse conditions = extreme risk
        risk_factors = 0

        if self._is_fii_selling_spree():
            risk_factors += 1

        if self._is_election_period():
            risk_factors += 1

        if self._is_rbi_policy_week():
            risk_factors += 1

        if self._is_high_volatility_regime():
            risk_factors += 1

        return risk_factors >= 2  # 2+ risk factors = extreme risk

    def _should_allow_trading(self) -> bool:
        """
        Determine if trading should be allowed in current regime

        Returns:
            True if trading allowed, False if kill switch activated
        """
        # Kill switches - NO TRADING in these regimes
        no_trade_regimes = [
            MarketRegime.EXTREME_RISK,
            MarketRegime.FII_SELLING_SPREE,
            MarketRegime.ELECTION_PERIOD,
            MarketRegime.RBI_POLICY_WEEK
        ]

        return self.current_regime not in no_trade_regimes

    def _calculate_risk_level(self) -> float:
        """
        Calculate current risk level (0-1 scale)

        Returns:
            Risk level from 0 (safe) to 1 (extreme risk)
        """
        risk_mapping = {
            MarketRegime.NORMAL: 0.1,
            MarketRegime.HIGH_VOLATILITY: 0.4,
            MarketRegime.LOW_LIQUIDITY: 0.5,
            MarketRegime.RBI_POLICY_WEEK: 0.7,
            MarketRegime.ELECTION_PERIOD: 0.8,
            MarketRegime.FII_SELLING_SPREE: 0.9,
            MarketRegime.EXTREME_RISK: 1.0
        }

        return risk_mapping.get(self.current_regime, 0.5)

    def _get_regime_reason(self) -> str:
        """
        Get human-readable reason for current regime

        Returns:
            Explanation of current regime
        """
        reasons = {
            MarketRegime.NORMAL: "Normal market conditions",
            MarketRegime.FII_SELLING_SPREE: f"FII selling >{abs(self.fii_selling_threshold_crores)} crores for {self.fii_selling_days_threshold}+ days",
            MarketRegime.ELECTION_PERIOD: f"Within {self.election_blackout_days} days of election",
            MarketRegime.RBI_POLICY_WEEK: f"Within {self.rbi_policy_blackout_days} days of RBI policy",
            MarketRegime.HIGH_VOLATILITY: f"VIX above {self.high_volatility_vix_threshold}",
            MarketRegime.LOW_LIQUIDITY: f"Volume below {self.low_liquidity_volume_threshold*100}% of average",
            MarketRegime.EXTREME_RISK: "Multiple risk factors detected - TRADING HALTED"
        }

        return reasons.get(self.current_regime, "Unknown regime")

    def _get_next_review_date(self) -> datetime:
        """
        Get next regime review date

        Returns:
            Next review datetime
        """
        # Review daily during risky periods, weekly during normal
        if self.current_regime == MarketRegime.NORMAL:
            return datetime.now() + timedelta(days=7)
        else:
            return datetime.now() + timedelta(days=1)