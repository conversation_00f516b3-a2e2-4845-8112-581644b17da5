# Data Sources for Options Manipulation Detection

## 🆓 **Free Data Sources (No API Key Required)**

### **1. NSE (National Stock Exchange) - Primary Source**

#### **Options Chain Data**
- **URL**: `https://www.nseindia.com/api/option-chain-indices?symbol={SYMBOL}`
- **Symbols**: NIFTY, BANKNIFTY, FINNIFTY
- **Data**: Strike prices, premiums, OI, volume, Greeks
- **Frequency**: Real-time (updated every few seconds)
- **Rate Limit**: 60 requests/minute (built-in throttling)

#### **Market Status**
- **URL**: `https://www.nseindia.com/api/marketStatus`
- **Data**: Market open/close status, trading sessions
- **Frequency**: Real-time

#### **Historical Data**
- **URL**: `https://www.nseindia.com/api/historical/cm/equity`
- **Data**: Historical prices and volumes
- **Frequency**: Daily updates

### **2. BSE (Bombay Stock Exchange) - Secondary Source**
- **URL**: `https://api.bseindia.com/`
- **Status**: Free tier available
- **Data**: Options data for BSE-listed derivatives

## 🔑 **Premium Data Sources (API Key Required)**

### **1. Alpha Vantage**
- **Website**: https://www.alphavantage.co/
- **Free Tier**: 5 API requests per minute, 500 per day
- **Premium**: $49.99/month for higher limits
- **Data**: Real-time and historical options data
- **Coverage**: Global markets including Indian exchanges

```bash
# Environment variables
ALPHA_VANTAGE_API_KEY=your_api_key_here
```

### **2. Polygon.io**
- **Website**: https://polygon.io/
- **Free Tier**: 5 API calls per minute
- **Premium**: $99/month for real-time data
- **Data**: Options chains, trades, quotes
- **Coverage**: US markets (limited Indian coverage)

```bash
# Environment variables
POLYGON_API_KEY=your_api_key_here
```

### **3. IEX Cloud**
- **Website**: https://iexcloud.io/
- **Free Tier**: 500,000 core data calls per month
- **Premium**: Starting at $9/month
- **Data**: Real-time market data
- **Coverage**: Primarily US markets

```bash
# Environment variables
IEX_CLOUD_API_KEY=your_api_key_here
```

### **4. Quandl (Nasdaq Data Link)**
- **Website**: https://data.nasdaq.com/
- **Free Tier**: 50 calls per day
- **Premium**: $49/month and up
- **Data**: Financial and economic data
- **Coverage**: Global markets including India

```bash
# Environment variables
QUANDL_API_KEY=your_api_key_here
```

## 🏦 **Broker APIs (Account Required)**

### **1. Zerodha Kite Connect**
- **Website**: https://kite.trade/
- **Cost**: ₹2,000/month
- **Data**: Real-time market data, order placement
- **Coverage**: NSE, BSE, MCX
- **Features**: Live market feed, historical data

```bash
# Environment variables
ZERODHA_API_KEY=your_api_key
ZERODHA_API_SECRET=your_api_secret
ZERODHA_ACCESS_TOKEN=your_access_token
```

### **2. Upstox API**
- **Website**: https://upstox.com/developer/
- **Cost**: Free for personal use, paid for commercial
- **Data**: Real-time quotes, historical data
- **Coverage**: NSE, BSE, MCX

```bash
# Environment variables
UPSTOX_API_KEY=your_api_key
UPSTOX_API_SECRET=your_api_secret
```

### **3. Angel Broking SmartAPI**
- **Website**: https://smartapi.angelbroking.com/
- **Cost**: Free for clients
- **Data**: Real-time market data
- **Coverage**: NSE, BSE, MCX

```bash
# Environment variables
ANGEL_API_KEY=your_api_key
ANGEL_CLIENT_ID=your_client_id
```

## 🔧 **Configuration Setup**

### **Current Setup (Free NSE APIs)**
```python
# No API key required - uses browser-like headers
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'application/json',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive'
}
```

### **Adding Premium Sources**
1. **Update .env file**:
```bash
# Choose your preferred provider
NSE_API_KEY=your_api_key_here
NSE_API_SECRET=your_secret_here

# Or use alternative providers
ALPHA_VANTAGE_API_KEY=your_key
POLYGON_API_KEY=your_key
```

2. **Update collector configuration**:
```python
# In data_collectors/premium_collector.py
class PremiumDataCollector:
    def __init__(self):
        self.api_key = settings.nse_api.api_key
        self.api_secret = settings.nse_api.api_secret
```

## 📊 **Data Quality Comparison**

| Source | Latency | Accuracy | Coverage | Cost | Rate Limits |
|--------|---------|----------|----------|------|-------------|
| NSE Free | ~5-10s | High | NSE Only | Free | 60/min |
| BSE Free | ~10-15s | High | BSE Only | Free | 100/min |
| Alpha Vantage | ~1-5s | High | Global | $49/mo | 5/min free |
| Zerodha Kite | ~1-2s | Very High | NSE/BSE | ₹2000/mo | 3/sec |
| Upstox | ~1-3s | High | NSE/BSE | Free* | 10/sec |

## 🚨 **Rate Limiting & Best Practices**

### **NSE Free APIs**
```python
# Built-in rate limiting
rate_limiter = Throttler(
    rate_limit=2,  # 2 requests per second
    period=1.0
)

# Respect daily limits
daily_requests = 0
MAX_DAILY_REQUESTS = 5000
```

### **Error Handling**
```python
# Handle common API errors
try:
    response = await make_request(url)
except RateLimitError:
    await asyncio.sleep(60)  # Wait 1 minute
except AuthenticationError:
    logger.error("API key invalid or expired")
except QuotaExceededError:
    logger.error("Daily quota exceeded")
```

## 🔄 **Fallback Strategy**

### **Primary → Secondary → Tertiary**
1. **Primary**: NSE Free APIs
2. **Secondary**: BSE Free APIs  
3. **Tertiary**: Premium APIs (if configured)

```python
async def collect_with_fallback(symbol):
    try:
        return await nse_collector.get_options_chain(symbol)
    except Exception:
        try:
            return await bse_collector.get_options_chain(symbol)
        except Exception:
            if premium_api_available:
                return await premium_collector.get_options_chain(symbol)
            raise DataCollectionError("All sources failed")
```

## 🛡️ **Security Considerations**

### **API Key Management**
- Store keys in environment variables
- Use secrets management in production
- Rotate keys regularly
- Monitor usage and costs

### **IP Whitelisting**
Some premium providers require IP whitelisting:
```bash
# Add your server IPs to provider whitelist
SERVER_IP_1=***********
SERVER_IP_2=***********
```

## 📈 **Monitoring Data Sources**

### **Health Checks**
```python
# Monitor data source health
async def check_data_source_health():
    sources = {
        'nse': await nse_collector.health_check(),
        'bse': await bse_collector.health_check(),
        'premium': await premium_collector.health_check()
    }
    return sources
```

### **Metrics to Track**
- Request success rate
- Response latency
- Data freshness
- API quota usage
- Error rates by source

## 🚀 **Getting Started**

### **Quick Start (Free)**
```bash
# No setup required - uses free NSE APIs
python main.py --mode detection
```

### **Premium Setup**
```bash
# 1. Get API key from provider
# 2. Update .env file
echo "NSE_API_KEY=your_key" >> .env

# 3. Restart system
docker-compose restart
```

### **Testing Data Sources**
```bash
# Test NSE connection
curl "http://localhost:8080/test/nse"

# Test all sources
curl "http://localhost:8080/test/all-sources"
```

## 📞 **Support & Documentation**

### **NSE API Issues**
- **Documentation**: https://www.nseindia.com/
- **Support**: Contact NSE technical support
- **Status Page**: Check NSE website for outages

### **Premium Provider Support**
- **Alpha Vantage**: <EMAIL>
- **Polygon**: <EMAIL>
- **Zerodha**: <EMAIL>

## ⚖️ **Legal Considerations**

### **Terms of Service**
- Review each provider's terms of service
- Ensure compliance with usage limits
- Respect data redistribution policies
- Consider commercial licensing for business use

### **Data Usage Rights**
- NSE data is for personal use only
- Commercial use may require licensing
- Check local regulations for financial data usage
- Implement proper attribution where required

---

**Summary**: The system works out-of-the-box with free NSE APIs (no API key required). Premium sources can be added for enhanced reliability and lower latency, but are optional for basic functionality.
