#!/usr/bin/env python3
"""
Realistic Backtesting Framework for Options Manipulation Detection
Includes actual NSE spreads, partial fills, rejection rates, and market impact
"""
import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import random

from models.data_models import OptionsData, ManipulationSignal, PatternType
from paper_trading.paper_trader import TradeAction, PaperTrade
from utils.execution_cost_model import execution_cost_model, OrderType

logger = logging.getLogger(__name__)

class FillType(str, Enum):
    """Types of order fills"""
    FULL = "FULL"
    PARTIAL = "PARTIAL"
    REJECTED = "REJECTED"
    SLIPPED = "SLIPPED"

@dataclass
class MarketConditions:
    """Market conditions affecting execution"""
    volatility_regime: str = "normal"  # low, normal, high, extreme
    liquidity_score: float = 1.0       # 0.0 to 1.0
    spread_multiplier: float = 1.0     # Multiplier for bid-ask spreads
    volume_percentile: float = 0.5     # Current volume vs historical
    time_of_day: str = "regular"       # pre_market, regular, post_market
    
@dataclass
class ExecutionResult:
    """Result of order execution in backtest"""
    fill_type: FillType
    executed_price: float
    executed_quantity: int
    slippage_bps: float
    execution_delay_ms: float
    rejection_reason: Optional[str] = None
    market_impact_bps: float = 0.0
    
@dataclass
class BacktestTrade:
    """Trade in backtesting with realistic execution"""
    signal: ManipulationSignal
    intended_action: TradeAction
    intended_price: float
    intended_quantity: int
    execution_result: ExecutionResult
    entry_time: datetime
    exit_time: Optional[datetime] = None
    exit_execution: Optional[ExecutionResult] = None
    profit_loss: float = 0.0
    
class RealisticBacktester:
    """
    Realistic backtesting engine that simulates actual NSE trading conditions
    """
    
    def __init__(self):
        # NSE-specific parameters based on real market data
        self.nse_parameters = {
            # Spread characteristics (basis points)
            'min_spread_bps': 10,      # Minimum 0.1% spread
            'normal_spread_bps': 50,   # Normal 0.5% spread
            'wide_spread_bps': 200,    # Wide 2.0% spread during volatility
            
            # Liquidity parameters
            'min_liquidity_lots': 10,   # Minimum liquidity in lots
            'normal_liquidity_lots': 100,
            'high_liquidity_lots': 500,
            
            # Execution probabilities
            'full_fill_prob': 0.7,     # 70% chance of full fill in normal conditions
            'partial_fill_prob': 0.2,  # 20% chance of partial fill
            'rejection_prob': 0.1,     # 10% chance of rejection
            
            # Slippage parameters (basis points)
            'min_slippage_bps': 5,
            'normal_slippage_bps': 25,
            'high_slippage_bps': 100,
            
            # Execution delays (milliseconds)
            'min_execution_delay': 50,
            'normal_execution_delay': 200,
            'high_execution_delay': 1000,
        }
        
        # Market impact model parameters
        self.market_impact_params = {
            'base_impact_bps': 2,      # Base market impact
            'volume_impact_factor': 0.5, # Impact scales with volume
            'liquidity_impact_factor': 1.0, # Impact scales with illiquidity
        }
        
        self.executed_trades: List[BacktestTrade] = []
        self.performance_metrics = {}
        
    def simulate_market_conditions(self, timestamp: datetime, option: OptionsData) -> MarketConditions:
        """
        Simulate realistic market conditions based on time and option characteristics
        """
        # Determine time of day
        hour = timestamp.hour
        if hour < 9 or hour > 15:
            time_of_day = "pre_post_market"
        elif 9 <= hour <= 15:
            time_of_day = "regular"
        else:
            time_of_day = "regular"
        
        # Simulate volatility regime based on option characteristics
        if option.volume > 1000:
            volatility_regime = "high"
            liquidity_score = 0.8
        elif option.volume > 500:
            volatility_regime = "normal" 
            liquidity_score = 0.6
        else:
            volatility_regime = "low"
            liquidity_score = 0.3
            
        # Add some randomness to simulate market dynamics
        liquidity_score *= random.uniform(0.7, 1.3)
        liquidity_score = max(0.1, min(1.0, liquidity_score))
        
        # Spread multiplier based on conditions
        if volatility_regime == "high":
            spread_multiplier = random.uniform(1.5, 3.0)
        elif volatility_regime == "low":
            spread_multiplier = random.uniform(2.0, 4.0)  # Low liquidity = wider spreads
        else:
            spread_multiplier = random.uniform(1.0, 2.0)
            
        return MarketConditions(
            volatility_regime=volatility_regime,
            liquidity_score=liquidity_score,
            spread_multiplier=spread_multiplier,
            volume_percentile=min(option.volume / 1000, 1.0),
            time_of_day=time_of_day
        )
    
    def calculate_realistic_spread(self, option: OptionsData, conditions: MarketConditions) -> Tuple[float, float]:
        """
        Calculate realistic bid-ask spread based on market conditions
        """
        base_spread_bps = self.nse_parameters['normal_spread_bps']
        
        # Adjust for market conditions
        spread_bps = base_spread_bps * conditions.spread_multiplier
        
        # Adjust for liquidity
        if conditions.liquidity_score < 0.3:
            spread_bps *= 2.0  # Double spread for illiquid options
        elif conditions.liquidity_score > 0.8:
            spread_bps *= 0.7  # Tighter spread for liquid options
            
        # Convert to price
        spread_amount = (spread_bps / 10000) * option.last_price
        
        # Calculate realistic bid/ask
        mid_price = option.last_price
        bid_price = mid_price - (spread_amount / 2)
        ask_price = mid_price + (spread_amount / 2)
        
        return max(bid_price, 0.05), ask_price  # Minimum bid of 5 paisa
    
    def simulate_execution(
        self, 
        option: OptionsData, 
        action: TradeAction, 
        quantity: int, 
        conditions: MarketConditions
    ) -> ExecutionResult:
        """
        Simulate realistic order execution with NSE characteristics
        """
        bid_price, ask_price = self.calculate_realistic_spread(option, conditions)
        
        # Determine execution probability based on conditions
        if conditions.liquidity_score > 0.7:
            full_fill_prob = self.nse_parameters['full_fill_prob']
        elif conditions.liquidity_score > 0.4:
            full_fill_prob = self.nse_parameters['full_fill_prob'] * 0.8
        else:
            full_fill_prob = self.nse_parameters['full_fill_prob'] * 0.5
            
        # Simulate execution outcome
        rand = random.random()
        
        if rand < full_fill_prob:
            fill_type = FillType.FULL
            executed_quantity = quantity
        elif rand < full_fill_prob + self.nse_parameters['partial_fill_prob']:
            fill_type = FillType.PARTIAL
            executed_quantity = int(quantity * random.uniform(0.3, 0.8))
        else:
            fill_type = FillType.REJECTED
            executed_quantity = 0
            return ExecutionResult(
                fill_type=fill_type,
                executed_price=0.0,
                executed_quantity=0,
                slippage_bps=0.0,
                execution_delay_ms=self.nse_parameters['normal_execution_delay'],
                rejection_reason="Insufficient liquidity"
            )
        
        # Calculate execution price with slippage
        if action == TradeAction.BUY:
            base_price = ask_price
        else:
            base_price = bid_price
            
        # Add slippage based on market conditions
        slippage_bps = self.calculate_slippage(quantity, conditions)
        slippage_amount = (slippage_bps / 10000) * base_price
        
        if action == TradeAction.BUY:
            executed_price = base_price + slippage_amount
        else:
            executed_price = base_price - slippage_amount
            
        # Calculate market impact
        market_impact_bps = self.calculate_market_impact(quantity, conditions)
        
        # Execution delay
        if conditions.volatility_regime == "high":
            execution_delay = random.uniform(
                self.nse_parameters['normal_execution_delay'],
                self.nse_parameters['high_execution_delay']
            )
        else:
            execution_delay = random.uniform(
                self.nse_parameters['min_execution_delay'],
                self.nse_parameters['normal_execution_delay']
            )
        
        return ExecutionResult(
            fill_type=fill_type,
            executed_price=max(executed_price, 0.05),  # Minimum price
            executed_quantity=executed_quantity,
            slippage_bps=slippage_bps,
            execution_delay_ms=execution_delay,
            market_impact_bps=market_impact_bps
        )
    
    def calculate_slippage(self, quantity: int, conditions: MarketConditions) -> float:
        """Calculate realistic slippage based on order size and conditions"""
        base_slippage = self.nse_parameters['normal_slippage_bps']
        
        # Scale with quantity (larger orders have more slippage)
        quantity_factor = min(quantity / 100, 3.0)  # Cap at 3x for very large orders
        
        # Scale with liquidity (less liquid = more slippage)
        liquidity_factor = 2.0 - conditions.liquidity_score
        
        # Scale with volatility
        if conditions.volatility_regime == "high":
            volatility_factor = 2.0
        elif conditions.volatility_regime == "low":
            volatility_factor = 1.5  # Low liquidity can also cause high slippage
        else:
            volatility_factor = 1.0
            
        total_slippage = base_slippage * quantity_factor * liquidity_factor * volatility_factor
        
        return min(total_slippage, 500)  # Cap at 5% slippage
    
    def calculate_market_impact(self, quantity: int, conditions: MarketConditions) -> float:
        """Calculate market impact of the trade"""
        base_impact = self.market_impact_params['base_impact_bps']
        
        # Impact scales with order size
        volume_impact = (quantity / 100) * self.market_impact_params['volume_impact_factor']
        
        # Impact scales with illiquidity
        liquidity_impact = (1.0 - conditions.liquidity_score) * self.market_impact_params['liquidity_impact_factor']
        
        return base_impact + volume_impact + liquidity_impact

    async def backtest_signal(
        self,
        signal: ManipulationSignal,
        market_data: List[OptionsData],
        trade_action: TradeAction,
        quantity: int
    ) -> Optional[BacktestTrade]:
        """
        Backtest a single manipulation signal with realistic execution
        """
        # Find the relevant option
        relevant_option = None
        for option in market_data:
            if (signal.symbols_affected and
                f"{option.symbol}_{option.strike}_{option.option_type.value}" in signal.symbols_affected[0]):
                relevant_option = option
                break

        if not relevant_option:
            logger.warning(f"No matching option found for signal: {signal.symbols_affected}")
            return None

        # Simulate market conditions
        conditions = self.simulate_market_conditions(signal.timestamp, relevant_option)

        # Simulate entry execution
        entry_execution = self.simulate_execution(
            relevant_option, trade_action, quantity, conditions
        )

        if entry_execution.fill_type == FillType.REJECTED:
            logger.info(f"Trade rejected: {entry_execution.rejection_reason}")
            return None

        # Create backtest trade
        trade = BacktestTrade(
            signal=signal,
            intended_action=trade_action,
            intended_price=relevant_option.last_price,
            intended_quantity=quantity,
            execution_result=entry_execution,
            entry_time=signal.timestamp
        )

        # Simulate exit after some time (for now, assume 1 hour holding period)
        exit_time = signal.timestamp + timedelta(hours=1)

        # Simulate price movement (simplified - in real backtest would use actual historical data)
        price_change_pct = random.uniform(-0.15, 0.15)  # ±15% price movement
        exit_price = relevant_option.last_price * (1 + price_change_pct)

        # Create exit option data
        exit_option = OptionsData(
            symbol=relevant_option.symbol,
            strike=relevant_option.strike,
            option_type=relevant_option.option_type,
            expiry_date=relevant_option.expiry_date,
            last_price=exit_price,
            bid_price=exit_price * 0.98,
            ask_price=exit_price * 1.02,
            bid_qty=relevant_option.bid_qty,
            ask_qty=relevant_option.ask_qty,
            volume=relevant_option.volume,
            open_interest=relevant_option.open_interest,
            timestamp=exit_time
        )

        # Simulate exit conditions (different liquidity at exit)
        exit_conditions = self.simulate_market_conditions(exit_time, exit_option)

        # Simulate exit execution (opposite action)
        exit_action = TradeAction.SELL if trade_action == TradeAction.BUY else TradeAction.BUY
        exit_execution = self.simulate_execution(
            exit_option, exit_action, entry_execution.executed_quantity, exit_conditions
        )

        # Calculate P&L
        if trade_action == TradeAction.BUY:
            gross_pnl = (exit_execution.executed_price - entry_execution.executed_price) * entry_execution.executed_quantity * 50
        else:
            gross_pnl = (entry_execution.executed_price - exit_execution.executed_price) * entry_execution.executed_quantity * 50

        # Subtract execution costs
        entry_costs = execution_cost_model.calculate_execution_costs(
            relevant_option, entry_execution.executed_quantity, OrderType.MARKET, trade_action == TradeAction.BUY
        )
        exit_costs = execution_cost_model.calculate_execution_costs(
            exit_option, exit_execution.executed_quantity, OrderType.MARKET, exit_action == TradeAction.BUY
        )

        net_pnl = gross_pnl - entry_costs.total_cost - exit_costs.total_cost

        trade.exit_time = exit_time
        trade.exit_execution = exit_execution
        trade.profit_loss = net_pnl

        self.executed_trades.append(trade)

        return trade

    def calculate_performance_metrics(self) -> Dict[str, Any]:
        """
        Calculate comprehensive performance metrics from backtest results
        """
        if not self.executed_trades:
            return {"error": "No trades executed"}

        trades = self.executed_trades
        profits = [t.profit_loss for t in trades if t.profit_loss is not None]

        if not profits:
            return {"error": "No profitable/loss data available"}

        # Basic metrics
        total_trades = len(trades)
        winning_trades = len([p for p in profits if p > 0])
        losing_trades = len([p for p in profits if p < 0])
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

        total_pnl = sum(profits)
        avg_pnl_per_trade = total_pnl / total_trades if total_trades > 0 else 0

        # Risk metrics
        max_profit = max(profits) if profits else 0
        max_loss = min(profits) if profits else 0

        # Execution quality metrics
        full_fills = len([t for t in trades if t.execution_result.fill_type == FillType.FULL])
        partial_fills = len([t for t in trades if t.execution_result.fill_type == FillType.PARTIAL])
        rejections = len([t for t in trades if t.execution_result.fill_type == FillType.REJECTED])

        avg_slippage = np.mean([t.execution_result.slippage_bps for t in trades if t.execution_result.slippage_bps])
        avg_execution_delay = np.mean([t.execution_result.execution_delay_ms for t in trades])

        # Calculate realistic breakeven win rate
        avg_win = np.mean([p for p in profits if p > 0]) if winning_trades > 0 else 0
        avg_loss = abs(np.mean([p for p in profits if p < 0])) if losing_trades > 0 else 0

        # Breakeven calculation: win_rate * avg_win = (1 - win_rate) * avg_loss
        # Solving: win_rate = avg_loss / (avg_win + avg_loss)
        breakeven_win_rate = (avg_loss / (avg_win + avg_loss)) * 100 if (avg_win + avg_loss) > 0 else 0

        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate_percent": win_rate,
            "breakeven_win_rate_percent": breakeven_win_rate,
            "win_rate_vs_breakeven": win_rate - breakeven_win_rate,
            "total_pnl": total_pnl,
            "avg_pnl_per_trade": avg_pnl_per_trade,
            "max_profit": max_profit,
            "max_loss": max_loss,
            "profit_factor": abs(sum([p for p in profits if p > 0]) / sum([p for p in profits if p < 0])) if losing_trades > 0 else float('inf'),

            # Execution quality
            "full_fill_rate_percent": (full_fills / total_trades) * 100,
            "partial_fill_rate_percent": (partial_fills / total_trades) * 100,
            "rejection_rate_percent": (rejections / total_trades) * 100,
            "avg_slippage_bps": avg_slippage,
            "avg_execution_delay_ms": avg_execution_delay,

            # Reality check
            "trades_profitable": winning_trades,
            "trades_unprofitable": losing_trades,
            "system_viability": "VIABLE" if win_rate > breakeven_win_rate else "NOT VIABLE",
            "required_improvement": max(0, breakeven_win_rate - win_rate) if win_rate < breakeven_win_rate else 0
        }

# Global backtester instance
realistic_backtester = RealisticBacktester()
