"""
Realistic execution cost modeling for paper trading
Accounts for slippage, bid-ask spreads, exchange fees, and market impact
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import numpy as np
from enum import Enum

from models.data_models import OptionsData

logger = logging.getLogger(__name__)

class OrderType(str, Enum):
    """Order types with different cost structures"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_MARKET = "stop_market"
    STOP_LIMIT = "stop_limit"

class LiquidityTier(str, Enum):
    """Liquidity tiers affecting execution costs"""
    HIGH = "high"      # Tight spreads, low impact
    MEDIUM = "medium"  # Normal spreads, moderate impact
    LOW = "low"        # Wide spreads, high impact
    VERY_LOW = "very_low"  # Very wide spreads, extreme impact

@dataclass
class ExecutionCosts:
    """Comprehensive execution cost breakdown"""
    # Core costs
    bid_ask_spread_cost: float = 0.0
    slippage_cost: float = 0.0
    exchange_fees: float = 0.0
    market_impact_cost: float = 0.0
    
    # Additional costs
    brokerage_fees: float = 0.0
    regulatory_fees: float = 0.0
    financing_cost: float = 0.0
    
    # Metadata
    effective_price: float = 0.0
    theoretical_price: float = 0.0
    total_cost_bps: float = 0.0
    liquidity_tier: LiquidityTier = LiquidityTier.MEDIUM
    
    @property
    def total_cost(self) -> float:
        """Total execution cost in rupees"""
        return (self.bid_ask_spread_cost + self.slippage_cost + 
                self.exchange_fees + self.market_impact_cost + 
                self.brokerage_fees + self.regulatory_fees + self.financing_cost)
    
    @property
    def total_cost_percentage(self) -> float:
        """Total cost as percentage of trade value"""
        if self.theoretical_price > 0:
            return (self.total_cost / self.theoretical_price) * 100
        return 0.0

class ExecutionCostModel:
    """
    Realistic execution cost model for options trading
    Models real-world costs that destroy theoretical profits
    """
    
    def __init__(self):
        # Exchange fee structure (NSE options)
        self.exchange_fees = {
            'transaction_charge': 0.0005,  # 0.05% of turnover
            'sebi_charges': 0.000001,      # ₹1 per crore
            'stamp_duty': 0.00003,         # 0.003% on buy side only
            'gst': 0.18                    # 18% on brokerage and charges
        }
        
        # Brokerage structure (typical discount broker)
        self.brokerage_fees = {
            'per_order': 20.0,             # ₹20 per order
            'percentage': 0.0,             # 0% for options (flat fee model)
            'minimum': 0.0,
            'maximum': 20.0
        }
        
        # Market impact parameters
        self.market_impact_params = {
            LiquidityTier.HIGH: {'base_impact': 0.001, 'size_multiplier': 0.5},
            LiquidityTier.MEDIUM: {'base_impact': 0.002, 'size_multiplier': 1.0},
            LiquidityTier.LOW: {'base_impact': 0.005, 'size_multiplier': 2.0},
            LiquidityTier.VERY_LOW: {'base_impact': 0.01, 'size_multiplier': 4.0}
        }
        
        # Slippage parameters (additional to market impact)
        self.slippage_params = {
            OrderType.MARKET: 0.002,       # 0.2% average slippage for market orders
            OrderType.LIMIT: 0.0005,       # 0.05% for limit orders (when filled)
            OrderType.STOP_MARKET: 0.003,  # 0.3% for stop market orders
            OrderType.STOP_LIMIT: 0.001    # 0.1% for stop limit orders
        }
    
    def calculate_execution_costs(
        self, 
        option: OptionsData, 
        quantity: int, 
        order_type: OrderType = OrderType.MARKET,
        is_buy: bool = True
    ) -> ExecutionCosts:
        """
        Calculate comprehensive execution costs for an options trade
        
        Args:
            option: Options data for the trade
            quantity: Number of lots to trade
            order_type: Type of order
            is_buy: True for buy orders, False for sell orders
            
        Returns:
            ExecutionCosts with detailed breakdown
        """
        # Determine liquidity tier
        liquidity_tier = self._assess_liquidity_tier(option)
        
        # Calculate trade value
        lot_size = 50  # Standard lot size for index options
        trade_value = option.last_price * quantity * lot_size
        theoretical_price = option.last_price
        
        costs = ExecutionCosts(
            theoretical_price=theoretical_price,
            liquidity_tier=liquidity_tier
        )
        
        # 1. Bid-Ask Spread Cost
        costs.bid_ask_spread_cost = self._calculate_spread_cost(
            option, quantity, lot_size, is_buy
        )
        
        # 2. Market Impact Cost
        costs.market_impact_cost = self._calculate_market_impact(
            option, quantity, lot_size, liquidity_tier
        )
        
        # 3. Slippage Cost
        costs.slippage_cost = self._calculate_slippage(
            trade_value, order_type, liquidity_tier
        )
        
        # 4. Exchange Fees
        costs.exchange_fees = self._calculate_exchange_fees(trade_value, is_buy)
        
        # 5. Brokerage Fees
        costs.brokerage_fees = self._calculate_brokerage_fees(trade_value)
        
        # 6. Regulatory Fees
        costs.regulatory_fees = self._calculate_regulatory_fees(trade_value)
        
        # Calculate effective price
        total_cost_per_share = costs.total_cost / (quantity * lot_size)
        if is_buy:
            costs.effective_price = theoretical_price + total_cost_per_share
        else:
            costs.effective_price = theoretical_price - total_cost_per_share
        
        # Calculate total cost in basis points
        if theoretical_price > 0:
            costs.total_cost_bps = (costs.total_cost / trade_value) * 10000
        
        return costs
    
    def _assess_liquidity_tier(self, option: OptionsData) -> LiquidityTier:
        """Assess liquidity tier based on option characteristics"""
        # Calculate spread as percentage of mid price
        if option.bid_price > 0 and option.ask_price > 0:
            mid_price = (option.bid_price + option.ask_price) / 2
            spread_pct = (option.ask_price - option.bid_price) / mid_price
        else:
            spread_pct = 0.1  # Assume wide spread if no bid/ask
        
        # Consider volume and open interest
        volume_score = min(option.volume / 1000, 1.0)  # Normalize to 0-1
        oi_score = min(option.open_interest / 10000, 1.0)  # Normalize to 0-1
        
        # Combined liquidity score
        liquidity_score = (volume_score + oi_score) / 2 - spread_pct
        
        if liquidity_score > 0.7:
            return LiquidityTier.HIGH
        elif liquidity_score > 0.4:
            return LiquidityTier.MEDIUM
        elif liquidity_score > 0.1:
            return LiquidityTier.LOW
        else:
            return LiquidityTier.VERY_LOW
    
    def _calculate_spread_cost(
        self, option: OptionsData, quantity: int, lot_size: int, is_buy: bool
    ) -> float:
        """Calculate bid-ask spread cost"""
        if option.bid_price <= 0 or option.ask_price <= 0:
            # No valid bid/ask, assume 2% spread cost
            return option.last_price * quantity * lot_size * 0.02
        
        spread = option.ask_price - option.bid_price
        
        if is_buy:
            # Buy at ask, cost is difference from mid
            mid_price = (option.bid_price + option.ask_price) / 2
            cost_per_share = option.ask_price - mid_price
        else:
            # Sell at bid, cost is difference from mid
            mid_price = (option.bid_price + option.ask_price) / 2
            cost_per_share = mid_price - option.bid_price
        
        return cost_per_share * quantity * lot_size
    
    def _calculate_market_impact(
        self, option: OptionsData, quantity: int, lot_size: int, liquidity_tier: LiquidityTier
    ) -> float:
        """Calculate market impact cost based on trade size and liquidity"""
        trade_value = option.last_price * quantity * lot_size
        
        # Get impact parameters for liquidity tier
        params = self.market_impact_params[liquidity_tier]
        base_impact = params['base_impact']
        size_multiplier = params['size_multiplier']
        
        # Calculate size impact (square root model)
        # Larger trades have disproportionately higher impact
        size_factor = np.sqrt(quantity / 10)  # Normalized to 10 lots
        
        # Total impact percentage
        impact_pct = base_impact * size_factor * size_multiplier
        
        return trade_value * impact_pct
    
    def _calculate_slippage(
        self, trade_value: float, order_type: OrderType, liquidity_tier: LiquidityTier
    ) -> float:
        """Calculate slippage cost based on order type and liquidity"""
        base_slippage = self.slippage_params[order_type]
        
        # Adjust for liquidity
        liquidity_multiplier = {
            LiquidityTier.HIGH: 0.5,
            LiquidityTier.MEDIUM: 1.0,
            LiquidityTier.LOW: 2.0,
            LiquidityTier.VERY_LOW: 4.0
        }[liquidity_tier]
        
        adjusted_slippage = base_slippage * liquidity_multiplier
        return trade_value * adjusted_slippage
    
    def _calculate_exchange_fees(self, trade_value: float, is_buy: bool) -> float:
        """Calculate NSE exchange fees"""
        transaction_charge = trade_value * self.exchange_fees['transaction_charge']
        sebi_charges = trade_value * self.exchange_fees['sebi_charges']
        
        # Stamp duty only on buy side
        stamp_duty = trade_value * self.exchange_fees['stamp_duty'] if is_buy else 0
        
        # GST on all charges
        total_charges = transaction_charge + sebi_charges + stamp_duty
        gst = total_charges * self.exchange_fees['gst']
        
        return total_charges + gst
    
    def _calculate_brokerage_fees(self, trade_value: float) -> float:
        """Calculate brokerage fees"""
        # Flat fee model for options
        return self.brokerage_fees['per_order']
    
    def _calculate_regulatory_fees(self, trade_value: float) -> float:
        """Calculate additional regulatory fees"""
        # Minimal for options trading
        return 0.0
    
    def get_cost_summary(self, costs: ExecutionCosts) -> Dict[str, Any]:
        """Get human-readable cost summary"""
        return {
            'total_cost_rupees': costs.total_cost,
            'total_cost_percentage': costs.total_cost_percentage,
            'total_cost_bps': costs.total_cost_bps,
            'effective_price': costs.effective_price,
            'theoretical_price': costs.theoretical_price,
            'liquidity_tier': costs.liquidity_tier,
            'breakdown': {
                'spread_cost': costs.bid_ask_spread_cost,
                'market_impact': costs.market_impact_cost,
                'slippage': costs.slippage_cost,
                'exchange_fees': costs.exchange_fees,
                'brokerage': costs.brokerage_fees,
                'other': costs.regulatory_fees + costs.financing_cost
            }
        }
    
    def estimate_round_trip_costs(
        self, option: OptionsData, quantity: int, order_type: OrderType = OrderType.MARKET
    ) -> float:
        """Estimate total round-trip trading costs (buy + sell)"""
        buy_costs = self.calculate_execution_costs(option, quantity, order_type, is_buy=True)
        sell_costs = self.calculate_execution_costs(option, quantity, order_type, is_buy=False)
        
        return buy_costs.total_cost + sell_costs.total_cost

# Global execution cost model
execution_cost_model = ExecutionCostModel()
