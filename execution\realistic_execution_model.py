"""
Realistic Execution Model for Indian Options
The Architect's Fix #2: Build a Proper Execution Model

This module implements brutal reality: 2-5% slippage, 30% partial fills,
and market impact modeling. No more fantasy paper trading.
"""
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum
import random

logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    """Order execution status"""
    PENDING = "pending"
    PARTIAL_FILL = "partial_fill"
    FILLED = "filled"
    REJECTED = "rejected"
    CANCELLED = "cancelled"

class OrderType(Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"

@dataclass
class ExecutionResult:
    """Result of order execution"""
    order_id: str
    status: OrderStatus
    filled_quantity: int
    avg_fill_price: float
    total_slippage_bps: float
    execution_time_ms: float
    market_impact_bps: float
    fees_paid: float
    rejection_reason: Optional[str] = None

@dataclass
class MarketConditions:
    """Current market conditions affecting execution"""
    bid_ask_spread_bps: float
    volume_last_hour: int
    volatility_percentile: float  # 0-100
    time_to_expiry_hours: float
    open_interest: int
    is_near_expiry: bool  # <24 hours
    is_high_volatility: bool  # >80th percentile

class RealisticExecutionModel:
    """
    Brutal reality execution model for Indian options
    No fantasy fills, no perfect execution
    """

    def __init__(self, config: Dict = None):
        self.config = config or {}

        # Slippage parameters (basis points)
        self.base_slippage_bps = self.config.get('base_slippage_bps', 200)  # 2%
        self.max_slippage_bps = self.config.get('max_slippage_bps', 500)   # 5%

        # Partial fill probabilities
        self.partial_fill_probability = self.config.get('partial_fill_probability', 0.30)  # 30%
        self.rejection_probability = self.config.get('rejection_probability', 0.05)        # 5%

        # Market impact parameters
        self.impact_coefficient = self.config.get('impact_coefficient', 0.1)
        self.liquidity_threshold = self.config.get('liquidity_threshold', 50000)  # ₹50k daily volume

        # Execution timing
        self.min_execution_time_ms = self.config.get('min_execution_time_ms', 50)
        self.max_execution_time_ms = self.config.get('max_execution_time_ms', 1000)

        # Fee structure (Indian options)
        self.brokerage_per_lot = self.config.get('brokerage_per_lot', 20)  # ₹20 per lot
        self.stt_rate = self.config.get('stt_rate', 0.0005)  # 0.05% on premium
        self.transaction_charges = self.config.get('transaction_charges', 0.00053)  # 0.053%

    def execute_order(
        self,
        symbol: str,
        strike: float,
        option_type: str,
        quantity: int,
        order_type: OrderType,
        market_conditions: MarketConditions,
        current_price: float,
        limit_price: Optional[float] = None
    ) -> ExecutionResult:
        """
        Execute order with realistic slippage, partial fills, and market impact

        Args:
            symbol: Option symbol
            strike: Strike price
            option_type: CE or PE
            quantity: Number of lots
            order_type: Market, limit, or stop loss
            market_conditions: Current market state
            current_price: Current option price
            limit_price: Limit price if applicable

        Returns:
            ExecutionResult with brutal reality
        """
        order_id = f"{symbol}_{strike}_{option_type}_{datetime.now().strftime('%H%M%S')}"

        # Check for rejection first
        if self._should_reject_order(market_conditions, quantity):
            return ExecutionResult(
                order_id=order_id,
                status=OrderStatus.REJECTED,
                filled_quantity=0,
                avg_fill_price=0,
                total_slippage_bps=0,
                execution_time_ms=0,
                market_impact_bps=0,
                fees_paid=0,
                rejection_reason="Insufficient liquidity or extreme market conditions"
            )

        # Calculate market impact
        market_impact_bps = self._calculate_market_impact(quantity, market_conditions)

        # Calculate slippage
        slippage_bps = self._calculate_slippage(market_conditions, order_type, market_impact_bps)

        # Determine fill quantity
        filled_quantity = self._determine_fill_quantity(quantity, market_conditions)

        # Calculate execution price
        execution_price = self._calculate_execution_price(
            current_price, slippage_bps, market_impact_bps, order_type, limit_price
        )

        # Calculate execution time
        execution_time_ms = self._calculate_execution_time(market_conditions, order_type)

        # Calculate fees
        fees = self._calculate_fees(filled_quantity, execution_price, symbol)

        # Determine final status
        if filled_quantity == quantity:
            status = OrderStatus.FILLED
        elif filled_quantity > 0:
            status = OrderStatus.PARTIAL_FILL
        else:
            status = OrderStatus.REJECTED

        return ExecutionResult(
            order_id=order_id,
            status=status,
            filled_quantity=filled_quantity,
            avg_fill_price=execution_price,
            total_slippage_bps=slippage_bps,
            execution_time_ms=execution_time_ms,
            market_impact_bps=market_impact_bps,
            fees_paid=fees
        )

    def _should_reject_order(self, market_conditions: MarketConditions, quantity: int) -> bool:
        """
        Determine if order should be rejected

        Args:
            market_conditions: Current market state
            quantity: Order quantity

        Returns:
            True if order should be rejected
        """
        # Base rejection probability
        rejection_prob = self.rejection_probability

        # Increase rejection probability for adverse conditions
        if market_conditions.is_near_expiry:
            rejection_prob *= 2

        if market_conditions.is_high_volatility:
            rejection_prob *= 1.5

        if market_conditions.bid_ask_spread_bps > 1000:  # >10% spread
            rejection_prob *= 3

        if market_conditions.volume_last_hour < self.liquidity_threshold:
            rejection_prob *= 2

        # Large orders more likely to be rejected
        if quantity > 100:  # >100 lots
            rejection_prob *= 1.5

        return random.random() < min(rejection_prob, 0.5)  # Cap at 50%

    def _calculate_market_impact(self, quantity: int, market_conditions: MarketConditions) -> float:
        """
        Calculate market impact in basis points

        Args:
            quantity: Order quantity
            market_conditions: Current market state

        Returns:
            Market impact in basis points
        """
        # Base impact proportional to order size vs liquidity
        volume_ratio = quantity / max(market_conditions.volume_last_hour / 100, 1)  # Lots per hour
        base_impact = volume_ratio * self.impact_coefficient * 100  # Convert to bps

        # Amplify impact for adverse conditions
        if market_conditions.is_near_expiry:
            base_impact *= 2

        if market_conditions.is_high_volatility:
            base_impact *= 1.5

        if market_conditions.bid_ask_spread_bps > 500:  # >5% spread
            base_impact *= 1.3

        return min(base_impact, 300)  # Cap at 3%

    def _calculate_slippage(
        self,
        market_conditions: MarketConditions,
        order_type: OrderType,
        market_impact_bps: float
    ) -> float:
        """
        Calculate slippage in basis points

        Args:
            market_conditions: Current market state
            order_type: Type of order
            market_impact_bps: Market impact

        Returns:
            Slippage in basis points
        """
        # Base slippage starts with bid-ask spread
        base_slippage = market_conditions.bid_ask_spread_bps / 2

        # Add market impact
        total_slippage = base_slippage + market_impact_bps

        # Market orders get worse slippage
        if order_type == OrderType.MARKET:
            total_slippage *= 1.5

        # Near expiry gets worse slippage
        if market_conditions.is_near_expiry:
            total_slippage *= 1.8

        # High volatility gets worse slippage
        if market_conditions.is_high_volatility:
            total_slippage *= 1.4

        # Add random component (market noise)
        noise = random.uniform(-0.2, 0.2) * total_slippage
        total_slippage += noise

        return min(max(total_slippage, self.base_slippage_bps), self.max_slippage_bps)

    def _determine_fill_quantity(self, quantity: int, market_conditions: MarketConditions) -> int:
        """
        Determine how much of the order gets filled

        Args:
            quantity: Requested quantity
            market_conditions: Current market state

        Returns:
            Actual filled quantity
        """
        # Base partial fill probability
        partial_prob = self.partial_fill_probability

        # Increase partial fill probability for adverse conditions
        if market_conditions.is_near_expiry:
            partial_prob *= 1.5

        if market_conditions.volume_last_hour < self.liquidity_threshold:
            partial_prob *= 2

        if quantity > 50:  # Large orders
            partial_prob *= 1.3

        # Determine if partial fill occurs
        if random.random() < min(partial_prob, 0.8):  # Cap at 80%
            # Partial fill: 30-80% of requested quantity
            fill_ratio = random.uniform(0.3, 0.8)
            return max(1, int(quantity * fill_ratio))
        else:
            # Full fill
            return quantity

    def _calculate_execution_price(
        self,
        current_price: float,
        slippage_bps: float,
        market_impact_bps: float,
        order_type: OrderType,
        limit_price: Optional[float]
    ) -> float:
        """
        Calculate actual execution price

        Args:
            current_price: Current market price
            slippage_bps: Slippage in basis points
            market_impact_bps: Market impact in basis points
            order_type: Order type
            limit_price: Limit price if applicable

        Returns:
            Execution price
        """
        # Apply slippage (always adverse)
        total_impact_bps = slippage_bps + market_impact_bps
        execution_price = current_price * (1 + total_impact_bps / 10000)

        # For limit orders, check if limit is hit
        if order_type == OrderType.LIMIT and limit_price is not None:
            if execution_price > limit_price:
                # Limit order gets better price
                execution_price = limit_price

        return execution_price

    def _calculate_execution_time(self, market_conditions: MarketConditions, order_type: OrderType) -> float:
        """
        Calculate execution time in milliseconds

        Args:
            market_conditions: Current market state
            order_type: Order type

        Returns:
            Execution time in milliseconds
        """
        base_time = self.min_execution_time_ms

        # Market orders are faster
        if order_type == OrderType.MARKET:
            time_multiplier = 1.0
        else:
            time_multiplier = 2.0  # Limit orders take longer

        # Adverse conditions slow execution
        if market_conditions.is_high_volatility:
            time_multiplier *= 1.5

        if market_conditions.volume_last_hour < self.liquidity_threshold:
            time_multiplier *= 2.0

        execution_time = base_time * time_multiplier

        # Add random component
        noise = random.uniform(0.8, 1.2)
        execution_time *= noise

        return min(execution_time, self.max_execution_time_ms)

    def _calculate_fees(self, quantity: int, price: float, symbol: str) -> float:
        """
        Calculate total fees for Indian options trade

        Args:
            quantity: Number of lots
            price: Execution price
            symbol: Symbol name

        Returns:
            Total fees in rupees
        """
        # Get lot size
        lot_sizes = {"NIFTY": 50, "BANKNIFTY": 15, "FINNIFTY": 40}
        lot_size = lot_sizes.get(symbol, 50)

        # Calculate premium value
        premium_value = quantity * price * lot_size

        # Brokerage (per lot)
        brokerage = quantity * self.brokerage_per_lot

        # STT (on premium)
        stt = premium_value * self.stt_rate

        # Transaction charges
        transaction_charges = premium_value * self.transaction_charges

        # GST on brokerage and transaction charges (18%)
        gst = (brokerage + transaction_charges) * 0.18

        total_fees = brokerage + stt + transaction_charges + gst

        return total_fees

    def get_execution_summary(self, executions: List[ExecutionResult]) -> Dict:
        """
        Generate summary statistics for execution performance

        Args:
            executions: List of execution results

        Returns:
            Summary statistics
        """
        if not executions:
            return {}

        filled_executions = [e for e in executions if e.status in [OrderStatus.FILLED, OrderStatus.PARTIAL_FILL]]

        if not filled_executions:
            return {"fill_rate": 0, "rejection_rate": len(executions)}

        return {
            "total_orders": len(executions),
            "filled_orders": len([e for e in executions if e.status == OrderStatus.FILLED]),
            "partial_fills": len([e for e in executions if e.status == OrderStatus.PARTIAL_FILL]),
            "rejections": len([e for e in executions if e.status == OrderStatus.REJECTED]),
            "fill_rate": len(filled_executions) / len(executions),
            "avg_slippage_bps": np.mean([e.total_slippage_bps for e in filled_executions]),
            "avg_market_impact_bps": np.mean([e.market_impact_bps for e in filled_executions]),
            "avg_execution_time_ms": np.mean([e.execution_time_ms for e in filled_executions]),
            "total_fees": sum([e.fees_paid for e in filled_executions]),
            "avg_fill_ratio": np.mean([e.filled_quantity for e in filled_executions])
        }