"""
FII/DII Institutional Flow Detection Algorithm for Indian Markets
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

from detection.base_detector import BaseDetector
from models.data_models import ManipulationSignal, OptionsData, PatternType
from config.settings import settings
from utils.enhanced_logging import enhanced_logger

logger = logging.getLogger(__name__)

class IndianInstitutionalFlowDetector(BaseDetector):
    """
    Detects FII/DII institutional flow manipulation patterns in Indian options markets
    """

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("indian_institutional_flow_detector", config)

        # Indian market specific thresholds
        self.volume_sigma_threshold = self.config.get("volume_sigma_threshold", 3.0)
        self.oi_increase_threshold = self.config.get("oi_increase_threshold", 0.50)
        self.dte_threshold = self.config.get("dte_threshold", 7)
        self.moneyness_lower = self.config.get("moneyness_lower", 0.98)
        self.moneyness_upper = self.config.get("moneyness_upper", 1.02)
        self.notional_threshold_crores = self.config.get("notional_threshold_crores", 50.0)
        self.confidence_base = self.config.get("confidence_base", 0.7)

        # Indian market lot sizes
        self.lot_sizes = {
            "NIFTY": 50,
            "BANKNIFTY": 15,
            "FINNIFTY": 40
        }

    def get_required_data_window(self) -> int:
        """Minimum data points needed for institutional flow detection"""
        return 30  # Need more data for statistical significance

    async def detect(self, data: List[OptionsData]) -> List[ManipulationSignal]:
        """
        Detect FII/DII institutional flow manipulation patterns

        Args:
            data: List of options data to analyze

        Returns:
            List of detected institutional flow signals
        """
        signals = []

        try:
            enhanced_logger.logger.info("[INSTITUTIONAL FLOW] Starting FII/DII flow detection")

            # Convert to DataFrame for analysis
            df = self._prepare_dataframe(data)

            if df.empty:
                enhanced_logger.logger.warning("[WARN] No valid data for institutional flow detection")
                return signals

            # Group by symbol and analyze each separately
            for symbol in df['symbol'].unique():
                symbol_data = df[df['symbol'] == symbol]

                enhanced_logger.logger.info(f"[ANALYZE] Analyzing institutional flow for {symbol}")
                enhanced_logger.logger.info(f"   Data points: {len(symbol_data)}")

                # Detect institutional flow patterns
                symbol_signals = self._detect_institutional_flow_in_symbol(symbol_data, symbol)
                signals.extend(symbol_signals)

            enhanced_logger.logger.info(f"[OK] Institutional flow detector completed: {len(signals)} signals found")

        except Exception as e:
            logger.error(f"Error in institutional flow detection: {str(e)}")
            raise

        return signals

    def _prepare_dataframe(self, data: List[OptionsData]) -> pd.DataFrame:
        """
        Convert options data to DataFrame for analysis

        Args:
            data: List of options data

        Returns:
            Prepared DataFrame with calculated metrics
        """
        records = []
        for option in data:
            records.append({
                'symbol': option.symbol,
                'strike': option.strike,
                'option_type': option.option_type.value,
                'expiry_date': option.expiry_date,
                'timestamp': option.timestamp,
                'last_price': option.last_price,
                'bid_price': option.bid_price,
                'ask_price': option.ask_price,
                'bid_qty': option.bid_qty,
                'ask_qty': option.ask_qty,
                'volume': option.volume,
                'open_interest': option.open_interest
            })

        df = pd.DataFrame(records)

        if not df.empty:
            # Ensure timestamp is datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['expiry_date'] = pd.to_datetime(df['expiry_date'])

            # Calculate derived metrics
            df['mid_price'] = (df['bid_price'] + df['ask_price']) / 2
            df['total_qty'] = df['bid_qty'] + df['ask_qty']

            # Calculate days to expiry
            df['dte'] = (df['expiry_date'] - df['timestamp']).dt.days

            # Calculate notional value in crores
            lot_size_map = df['symbol'].map(self.lot_sizes).fillna(50)  # Default to NIFTY lot size
            df['notional_crores'] = (df['volume'] * df['last_price'] * lot_size_map) / 10000000  # Convert to crores

        return df

    def _detect_institutional_flow_in_symbol(self, df: pd.DataFrame, symbol: str) -> List[ManipulationSignal]:
        """
        Detect institutional flow patterns for a specific symbol

        Args:
            df: DataFrame with symbol data
            symbol: Symbol name

        Returns:
            List of institutional flow signals
        """
        signals = []

        # Calculate volume statistics for the symbol
        volume_stats = self._calculate_volume_statistics(df)

        # Get current underlying price (approximate from ATM options)
        current_price = self._estimate_underlying_price(df)

        if current_price is None:
            enhanced_logger.logger.warning(f"[WARN] Could not estimate underlying price for {symbol}")
            return signals

        # Group by strike and option type
        for (strike, option_type), group in df.groupby(['strike', 'option_type']):
            if len(group) < 5:  # Need minimum data points
                continue

            # Calculate moneyness
            moneyness = strike / current_price

            # Check if this strike is in our focus range (near ATM)
            if not (self.moneyness_lower <= moneyness <= self.moneyness_upper):
                continue

            # Sort by timestamp
            group = group.sort_values('timestamp').reset_index(drop=True)

            # Check for institutional flow patterns
            flow_signal = self._check_institutional_flow_pattern(
                group, symbol, strike, option_type, volume_stats, current_price
            )

            if flow_signal:
                signals.append(flow_signal)

        return signals

    def _calculate_volume_statistics(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate volume statistics for the symbol

        Args:
            df: DataFrame with symbol data

        Returns:
            Dictionary with volume statistics
        """
        # Calculate rolling statistics (simulate historical data)
        volumes = df['volume'].values

        if len(volumes) < 20:
            # Not enough data for proper statistics
            return {
                'mean': np.mean(volumes) if len(volumes) > 0 else 0,
                'std': np.std(volumes) if len(volumes) > 1 else 1,
                'threshold': np.mean(volumes) * 2 if len(volumes) > 0 else 1000
            }

        # Use last 20 data points as proxy for 20-day average
        recent_volumes = volumes[-20:]
        mean_volume = np.mean(recent_volumes)
        std_volume = np.std(recent_volumes)

        return {
            'mean': mean_volume,
            'std': std_volume,
            'threshold': mean_volume + (self.volume_sigma_threshold * std_volume)
        }

    def _estimate_underlying_price(self, df: pd.DataFrame) -> Optional[float]:
        """
        Estimate underlying price from options data

        Args:
            df: DataFrame with options data

        Returns:
            Estimated underlying price or None
        """
        # Find ATM options (closest to current price)
        # Use the strike with highest volume as proxy
        if df.empty:
            return None

        # Group by strike and sum volumes
        strike_volumes = df.groupby('strike')['volume'].sum()

        if strike_volumes.empty:
            return None

        # Return the strike with highest volume as proxy for underlying
        return strike_volumes.idxmax()

    def _check_institutional_flow_pattern(
        self,
        group: pd.DataFrame,
        symbol: str,
        strike: float,
        option_type: str,
        volume_stats: Dict[str, float],
        current_price: float
    ) -> Optional[ManipulationSignal]:
        """
        Check for institutional flow manipulation pattern

        Args:
            group: DataFrame for specific option
            symbol: Symbol name
            strike: Strike price
            option_type: Option type (CE/PE)
            volume_stats: Volume statistics
            current_price: Current underlying price

        Returns:
            ManipulationSignal if pattern detected, None otherwise
        """
        latest = group.iloc[-1]

        # Check volume threshold
        if latest['volume'] <= volume_stats['threshold']:
            return None

        # Check open interest increase (if we have historical data)
        oi_increase = 0
        if len(group) > 1:
            previous_oi = group.iloc[-2]['open_interest']
            current_oi = latest['open_interest']
            if previous_oi > 0:
                oi_increase = (current_oi - previous_oi) / previous_oi

        # Check days to expiry
        if latest['dte'] > self.dte_threshold:
            return None

        # Check notional value threshold
        if latest['notional_crores'] < self.notional_threshold_crores:
            return None

        # Calculate confidence
        confidence = self._calculate_confidence(
            latest, volume_stats, oi_increase, current_price
        )

        if confidence < 0.6:  # Minimum confidence threshold
            return None

        # Create signal
        enhanced_logger.logger.warning(f"[ALERT] INSTITUTIONAL FLOW DETECTED!")
        enhanced_logger.logger.warning(f"   Symbol: {symbol} {strike} {option_type}")
        enhanced_logger.logger.warning(f"   Volume: {latest['volume']:,} (threshold: {volume_stats['threshold']:,.0f})")
        enhanced_logger.logger.warning(f"   Notional: ₹{latest['notional_crores']:.1f} crores")
        enhanced_logger.logger.warning(f"   OI Increase: {oi_increase:.1%}")
        enhanced_logger.logger.warning(f"   DTE: {latest['dte']} days")
        enhanced_logger.logger.warning(f"   Confidence: {confidence:.1%}")

        return self._create_institutional_flow_signal(
            latest, symbol, strike, option_type, volume_stats,
            oi_increase, confidence, current_price
        )

    def _calculate_confidence(
        self,
        latest: pd.Series,
        volume_stats: Dict[str, float],
        oi_increase: float,
        current_price: float
    ) -> float:
        """
        Calculate confidence score for institutional flow detection

        Args:
            latest: Latest data point
            volume_stats: Volume statistics
            oi_increase: Open interest increase ratio
            current_price: Current underlying price

        Returns:
            Confidence score between 0 and 1
        """
        confidence = self.confidence_base

        # Volume factor
        volume_ratio = latest['volume'] / volume_stats['mean'] if volume_stats['mean'] > 0 else 1
        if volume_ratio > 5:
            confidence += 0.1
        if volume_ratio > 10:
            confidence += 0.1

        # Open interest factor
        if oi_increase > self.oi_increase_threshold:
            confidence += 0.1
        if oi_increase > self.oi_increase_threshold * 2:
            confidence += 0.05

        # Moneyness factor (closer to ATM = higher confidence)
        moneyness = latest['strike'] / current_price
        distance_from_atm = abs(moneyness - 1.0)
        if distance_from_atm < 0.01:  # Very close to ATM
            confidence += 0.1
        elif distance_from_atm < 0.02:  # Close to ATM
            confidence += 0.05

        # Notional value factor
        if latest['notional_crores'] > self.notional_threshold_crores * 2:
            confidence += 0.1

        # Time to expiry factor (shorter = higher confidence for manipulation)
        if latest['dte'] <= 3:
            confidence += 0.1
        elif latest['dte'] <= 5:
            confidence += 0.05

        return min(confidence, 0.95)  # Cap at 95%

    def _create_institutional_flow_signal(
        self,
        latest: pd.Series,
        symbol: str,
        strike: float,
        option_type: str,
        volume_stats: Dict[str, float],
        oi_increase: float,
        confidence: float,
        current_price: float
    ) -> ManipulationSignal:
        """
        Create manipulation signal for institutional flow

        Args:
            latest: Latest data point
            symbol: Symbol name
            strike: Strike price
            option_type: Option type
            volume_stats: Volume statistics
            oi_increase: Open interest increase
            confidence: Confidence score
            current_price: Current underlying price

        Returns:
            ManipulationSignal instance
        """
        # Estimate profit potential
        estimated_profit = self._estimate_profit(latest, symbol, current_price)

        # Create description
        description = (
            f"Institutional flow detected: ₹{latest['notional_crores']:.1f} crores "
            f"in {symbol} {strike} {option_type}, "
            f"volume {latest['volume']:,} ({latest['volume']/volume_stats['mean']:.1f}x avg), "
            f"OI +{oi_increase:.1%}, {latest['dte']} DTE"
        )

        signal = ManipulationSignal(
            pattern_type=PatternType.INSTITUTIONAL_FLOW,
            timestamp=latest['timestamp'],
            symbols_affected=[f"{symbol}_{strike}_{option_type}"],
            confidence=confidence,
            description=description,
            estimated_profit=estimated_profit,
            market_impact={
                "flow_type": "institutional",
                "notional_crores": latest['notional_crores'],
                "volume_ratio": latest['volume'] / volume_stats['mean'] if volume_stats['mean'] > 0 else 1,
                "oi_increase": oi_increase,
                "dte": latest['dte'],
                "moneyness": strike / current_price
            }
        )

        return signal

    def _estimate_profit(self, latest: pd.Series, symbol: str, current_price: float) -> float:
        """
        Estimate potential profit from institutional flow

        Args:
            latest: Latest data point
            symbol: Symbol name
            current_price: Current underlying price

        Returns:
            Estimated profit in rupees
        """
        # Base calculation: notional value * expected move percentage
        notional_rupees = latest['notional_crores'] * 10000000  # Convert to rupees

        # Estimate expected move based on volume and time to expiry
        volume_factor = min(latest['volume'] / 10000, 2.0)  # Cap at 2x
        time_factor = max(0.1, latest['dte'] / 7)  # Shorter time = higher impact

        # Expected move percentage (conservative estimate)
        expected_move_pct = 0.005 * volume_factor * time_factor  # 0.5% base move

        # Profit estimate (conservative)
        estimated_profit = notional_rupees * expected_move_pct * 0.1  # 10% capture rate

        return max(estimated_profit, 0)