#!/usr/bin/env python3
"""
Market hours validation for NSE trading
"""
from datetime import datetime, time
import pytz
import logging

logger = logging.getLogger(__name__)

class MarketHoursValidator:
    """
    Validates if NSE markets are currently open for trading
    """
    
    def __init__(self):
        self.timezone = pytz.timezone('Asia/Kolkata')
        
        # NSE trading hours (IST)
        self.market_open = time(9, 15)  # 9:15 AM
        self.market_close = time(15, 30)  # 3:30 PM
        
        # Pre-market session
        self.pre_market_open = time(9, 0)  # 9:00 AM
        self.pre_market_close = time(9, 15)  # 9:15 AM
        
        # After-market session  
        self.after_market_open = time(15, 40)  # 3:40 PM
        self.after_market_close = time(16, 0)  # 4:00 PM
        
        # Weekend days (Saturday=5, Sunday=6)
        self.weekend_days = [5, 6]
        
        # NSE holidays (simplified - should be loaded from external source)
        self.holidays_2025 = [
            # Add major NSE holidays here
            "2025-01-26",  # Republic Day
            "2025-03-14",  # <PERSON>li
            "2025-08-15",  # Independence Day
            "2025-10-02",  # <PERSON>
            # Add more holidays as needed
        ]
    
    def is_market_open(self) -> bool:
        """
        Check if NSE markets are currently open for regular trading
        
        Returns:
            bool: True if markets are open, False otherwise
        """
        now = datetime.now(self.timezone)
        current_time = now.time()
        current_date = now.date()
        
        # Check if it's a weekend
        if now.weekday() in self.weekend_days:
            logger.info(f"Market closed: Weekend ({now.strftime('%A')})")
            return False
        
        # Check if it's a holiday
        if current_date.strftime('%Y-%m-%d') in self.holidays_2025:
            logger.info(f"Market closed: Holiday ({current_date})")
            return False
        
        # Check if it's within trading hours
        if self.market_open <= current_time <= self.market_close:
            logger.info(f"Market OPEN: Current time {current_time} within trading hours")
            return True
        else:
            logger.info(f"Market CLOSED: Current time {current_time} outside trading hours ({self.market_open}-{self.market_close})")
            return False
    
    def is_pre_market_open(self) -> bool:
        """Check if pre-market session is open"""
        now = datetime.now(self.timezone)
        current_time = now.time()
        
        if now.weekday() in self.weekend_days:
            return False
            
        return self.pre_market_open <= current_time <= self.pre_market_close
    
    def is_after_market_open(self) -> bool:
        """Check if after-market session is open"""
        now = datetime.now(self.timezone)
        current_time = now.time()
        
        if now.weekday() in self.weekend_days:
            return False
            
        return self.after_market_open <= current_time <= self.after_market_close
    
    def get_market_status(self) -> dict:
        """
        Get comprehensive market status information
        
        Returns:
            dict: Market status details
        """
        now = datetime.now(self.timezone)
        
        return {
            "current_time": now.strftime('%Y-%m-%d %H:%M:%S %Z'),
            "is_market_open": self.is_market_open(),
            "is_pre_market": self.is_pre_market_open(),
            "is_after_market": self.is_after_market_open(),
            "is_weekend": now.weekday() in self.weekend_days,
            "is_holiday": now.date().strftime('%Y-%m-%d') in self.holidays_2025,
            "next_market_open": self._get_next_market_open(),
            "trading_hours": f"{self.market_open} - {self.market_close} IST"
        }
    
    def _get_next_market_open(self) -> str:
        """Calculate when markets will next open"""
        now = datetime.now(self.timezone)
        
        # If it's currently market hours, return current session
        if self.is_market_open():
            return "Currently open"
        
        # If it's the same day but before market open
        if now.time() < self.market_open and now.weekday() not in self.weekend_days:
            next_open = now.replace(hour=9, minute=15, second=0, microsecond=0)
            return next_open.strftime('%Y-%m-%d %H:%M:%S')
        
        # Otherwise, next trading day
        days_ahead = 1
        while True:
            next_day = now.replace(hour=9, minute=15, second=0, microsecond=0)
            next_day = next_day.replace(day=now.day + days_ahead)
            
            if next_day.weekday() not in self.weekend_days:
                return next_day.strftime('%Y-%m-%d %H:%M:%S')
            
            days_ahead += 1
            if days_ahead > 7:  # Safety break
                break
        
        return "Unknown"
    
    def should_allow_detection(self) -> tuple[bool, str]:
        """
        Determine if detection should be allowed based on market status
        
        Returns:
            tuple: (allow_detection, reason)
        """
        if self.is_market_open():
            return True, "Market is open for regular trading"
        
        if self.is_pre_market_open():
            return True, "Pre-market session is active"
        
        if self.is_after_market_open():
            return True, "After-market session is active"
        
        now = datetime.now(self.timezone)
        
        if now.weekday() in self.weekend_days:
            return False, f"Market closed: Weekend ({now.strftime('%A')})"
        
        if now.date().strftime('%Y-%m-%d') in self.holidays_2025:
            return False, f"Market closed: Holiday"
        
        return False, f"Market closed: Outside trading hours ({self.market_open}-{self.market_close} IST)"

# Global instance
market_validator = MarketHoursValidator()
