#!/usr/bin/env python3
"""
Setup script for Options Manipulation Detection System
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def check_docker():
    """Check if Docker is installed and running"""
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Docker: {result.stdout.strip()}")
        
        # Check if Docker daemon is running
        subprocess.run(['docker', 'ps'], 
                      capture_output=True, check=True)
        print("✅ Docker daemon is running")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker is not installed or not running")
        print("Please install Docker: https://docs.docker.com/get-docker/")
        return False

def check_docker_compose():
    """Check if Docker Compose is available"""
    try:
        result = subprocess.run(['docker', 'compose', 'version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Docker Compose: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        try:
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ Docker Compose: {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Docker Compose is not installed")
            return False

def create_env_file():
    """Create .env file from template"""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("📝 Please edit .env file with your configuration")
        return True
    else:
        print("❌ .env.example file not found")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        'logs',
        'data',
        'monitoring/prometheus',
        'monitoring/grafana/dashboards',
        'monitoring/grafana/datasources',
        'nginx',
        'scripts'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Created necessary directories")

def install_python_dependencies():
    """Install Python dependencies"""
    try:
        print("📦 Installing Python dependencies...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✅ Python dependencies installed")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install Python dependencies")
        return False

def create_monitoring_configs():
    """Create monitoring configuration files"""
    
    # Prometheus configuration
    prometheus_config = """
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'options-detector'
    static_configs:
      - targets: ['options_detector:8000']
    scrape_interval: 5s
    metrics_path: '/metrics/prometheus'

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
"""
    
    prometheus_dir = Path('monitoring')
    prometheus_dir.mkdir(exist_ok=True)
    
    with open(prometheus_dir / 'prometheus.yml', 'w') as f:
        f.write(prometheus_config)
    
    # Grafana datasource configuration
    grafana_datasource = """
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
"""
    
    grafana_dir = Path('monitoring/grafana/datasources')
    grafana_dir.mkdir(parents=True, exist_ok=True)
    
    with open(grafana_dir / 'prometheus.yml', 'w') as f:
        f.write(grafana_datasource)
    
    print("✅ Created monitoring configurations")

def create_nginx_config():
    """Create Nginx configuration"""
    nginx_config = """
events {
    worker_connections 1024;
}

http {
    upstream api {
        server options_detector:8080;
    }
    
    upstream grafana {
        server grafana:3000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /api/ {
            proxy_pass http://api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /grafana/ {
            proxy_pass http://grafana/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location / {
            proxy_pass http://api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
"""
    
    nginx_dir = Path('nginx')
    nginx_dir.mkdir(exist_ok=True)
    
    with open(nginx_dir / 'nginx.conf', 'w') as f:
        f.write(nginx_config)
    
    print("✅ Created Nginx configuration")

def run_tests():
    """Run basic tests"""
    try:
        print("🧪 Running tests...")
        subprocess.run([sys.executable, '-m', 'pytest', 'tests/', '-v'], 
                      check=True)
        print("✅ Tests passed")
        return True
    except subprocess.CalledProcessError:
        print("❌ Some tests failed")
        return False
    except FileNotFoundError:
        print("⚠️  pytest not found, skipping tests")
        return True

def main():
    """Main setup function"""
    print("🚀 Setting up Options Manipulation Detection System")
    print("=" * 60)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Create configuration files
    create_env_file()
    create_monitoring_configs()
    create_nginx_config()
    
    # Install dependencies
    if not install_python_dependencies():
        print("⚠️  Continue with manual dependency installation")
    
    # Check Docker (optional for development)
    docker_available = check_docker() and check_docker_compose()
    
    # Run tests
    run_tests()
    
    print("\n" + "=" * 60)
    print("🎉 Setup completed!")
    print("\nNext steps:")
    print("1. Edit .env file with your configuration")
    print("2. Choose your deployment method:")
    print("\n   📦 Docker (Recommended):")
    if docker_available:
        print("     docker-compose up -d")
    else:
        print("     Install Docker first, then: docker-compose up -d")
    
    print("\n   🐍 Python (Development):")
    print("     python main.py --mode full")
    
    print("\n   🔧 API only:")
    print("     python main.py --mode api")
    
    print("\n   🔍 Detection only:")
    print("     python main.py --mode detection")
    
    print("\n📊 Access points:")
    print("   API: http://localhost:8080")
    print("   Grafana: http://localhost:3000 (admin/admin123)")
    print("   Prometheus: http://localhost:9090")
    
    print("\n📚 Documentation:")
    print("   README.md - Full documentation")
    print("   API docs: http://localhost:8080/docs")

if __name__ == "__main__":
    main()
